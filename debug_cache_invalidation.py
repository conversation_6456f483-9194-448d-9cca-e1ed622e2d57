#!/usr/bin/env python3
"""Debug cache invalidation."""

import asyncio
from src.zenoo_rpc.cache.manager import CacheManager

async def test_cache_invalidation():
    """Test cache invalidation."""
    try:
        manager = CacheManager()
        await manager.setup_memory_cache()
        
        # Cache query result
        model = "res.partner"
        domain = [("is_company", "=", True)]
        result = [{"id": 1, "name": "Test Company"}]
        
        print("Caching query result...")
        cached = await manager.cache_query_result(model, domain, result, ttl=60)
        print(f"Cached: {cached}")
        
        # Retrieve cached result
        print("Retrieving cached result...")
        cached_result = await manager.get_cached_query_result(model, domain)
        print(f"Cached result: {cached_result}")
        
        # Check what keys exist
        if hasattr(manager.strategies['memory'], 'cache'):
            cache_keys = list(manager.strategies['memory'].cache.keys())
            print(f"Cache keys before invalidation: {cache_keys}")
        
        # Test cache invalidation
        print("Invalidating cache...")
        invalidated = await manager.invalidate_model(model)
        print(f"Invalidated count: {invalidated}")
        
        # Check what keys exist after invalidation
        if hasattr(manager.strategies['memory'], 'cache'):
            cache_keys = list(manager.strategies['memory'].cache.keys())
            print(f"Cache keys after invalidation: {cache_keys}")
        
        # Should be gone after invalidation
        print("Retrieving cached result after invalidation...")
        cached_result = await manager.get_cached_query_result(model, domain)
        print(f"Cached result after invalidation: {cached_result}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_cache_invalidation())
