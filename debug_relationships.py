#!/usr/bin/env python3
"""Debug script for relationship descriptors."""

import asyncio
from unittest.mock import AsyncMock

from src.zenoo_rpc.models.common import ResPartner

async def test_relationship_descriptors():
    """Test relationship field descriptors."""
    try:
        mock_client = AsyncMock()
        
        # Mock country data
        mock_client.search_read.return_value = [
            {"id": 1, "name": "United States", "display_name": "United States"}
        ]
        
        print("Creating ResPartner...")
        partner = ResPartner(
            id=1,
            name="Test Partner",
            country_id=1,
            client=mock_client
        )
        print(f"Partner created: {partner}")
        
        # Access relationship field should return LazyRelationship
        print("Accessing country_id...")
        country_rel = partner.country_id
        print(f"Country relationship: {country_rel}")
        print(f"Type: {type(country_rel)}")

        # Check if it's a LazyRelationship
        from src.zenoo_rpc.models.relationships import LazyRelationship
        if isinstance(country_rel, LazyRelationship):
            print("Got LazyRelationship!")
            print(f"Has load method: {hasattr(country_rel, 'load')}")

            # Loading should trigger client call
            print("Loading country data...")
            country_data = await country_rel.load()
            print(f"Country data: {country_data}")
            print(f"Client called: {mock_client.search_read.called}")
        else:
            print("Not a LazyRelationship, trying to await...")
            country_data = await country_rel
            print(f"Country data: {country_data}")
            print(f"Client called: {mock_client.search_read.called}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_relationship_descriptors())
