# Zenoo-RPC Project Structure

## Root Directory Layout
```
zenoo_rpc/
├── src/zenoo_rpc/           # Main source code (src layout)
├── tests/                   # Test suite
├── docs/                    # Documentation
├── examples/                # Usage examples
├── scripts/                 # Development scripts
├── .serena/                 # Serena configuration
├── pyproject.toml          # Project configuration
├── Makefile                # Development commands
└── README.md               # Project documentation
```

## Source Code Structure (`src/zenoo_rpc/`)
```
src/zenoo_rpc/
├── __init__.py             # Package exports and version
├── client.py               # Main ZenooClient class
├── exceptions/             # Exception hierarchy
│   ├── __init__.py
│   ├── base.py            # Base exception classes
│   └── mapping.py         # Odoo error mapping
├── transport/              # HTTP transport layer
│   ├── __init__.py
│   ├── httpx_transport.py # HTTPX-based transport
│   ├── session.py         # Session management
│   └── pool.py            # Connection pooling
├── models/                 # Pydantic models
│   ├── __init__.py
│   ├── base.py            # Base model classes
│   ├── common.py          # Common Odoo models
│   ├── fields.py          # Field definitions
│   ├── registry.py        # Model registry
│   └── relationships.py   # Model relationships
├── query/                  # Query builder
│   ├── __init__.py
│   ├── builder.py         # Main query builder
│   ├── expressions.py     # Query expressions
│   ├── filters.py         # Filter operations
│   └── lazy.py            # Lazy loading
├── cache/                  # Caching system
│   ├── __init__.py
│   ├── manager.py         # Cache manager
│   ├── backends.py        # Cache backends (memory, Redis)
│   ├── strategies.py      # Caching strategies (TTL, LRU)
│   ├── keys.py            # Cache key generation
│   ├── decorators.py      # Caching decorators
│   └── exceptions.py      # Cache-specific exceptions
├── transaction/            # Transaction management
│   ├── __init__.py
│   ├── manager.py         # Transaction manager
│   ├── context.py         # Transaction context
│   └── exceptions.py      # Transaction exceptions
├── batch/                  # Batch operations
│   ├── __init__.py
│   ├── manager.py         # Batch manager
│   ├── executor.py        # Batch executor
│   ├── operations.py      # Batch operations
│   ├── context.py         # Batch context
│   └── exceptions.py      # Batch exceptions
└── retry/                  # Retry mechanisms
    ├── __init__.py
    ├── strategies.py      # Retry strategies
    ├── policies.py        # Retry policies
    ├── decorators.py      # Retry decorators
    └── exceptions.py      # Retry exceptions
```

## Test Structure (`tests/`)
- Mirrors source structure with `test_*.py` files
- Includes integration tests and unit tests
- Uses pytest fixtures in `conftest.py`
- Separate directories for complex modules (transaction, transport, batch)