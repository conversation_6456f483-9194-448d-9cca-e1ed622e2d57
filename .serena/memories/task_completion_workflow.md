# Zenoo-RPC Task Completion Workflow

## Before Starting Development
1. **Environment setup**:
   ```bash
   make install-dev
   ```
2. **Check current status**:
   ```bash
   git status
   git pull origin main
   ```

## During Development
1. **Code formatting** (run frequently):
   ```bash
   make format
   ```
2. **Linting checks**:
   ```bash
   make lint
   ```
3. **Type checking**:
   ```bash
   make type-check
   ```

## Before Committing Changes
1. **Run all tests**:
   ```bash
   make test
   ```
2. **Check test coverage**:
   ```bash
   make test-coverage
   ```
3. **For Redis-related changes**:
   ```bash
   make setup-redis
   make test-redis
   ```
4. **Final quality check**:
   ```bash
   make format
   make lint
   make type-check
   ```

## Task Completion Checklist
- [ ] Code follows style guidelines (Black formatted)
- [ ] All linting checks pass (Ruff)
- [ ] Type checking passes (MyPy strict mode)
- [ ] All tests pass (pytest)
- [ ] Test coverage is maintained (>90%)
- [ ] New functionality has tests
- [ ] Documentation is updated if needed
- [ ] Examples are updated if API changed

## Git Workflow
1. **Create feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```
2. **Make changes and test**
3. **Commit with descriptive message**:
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```
4. **Push and create PR**:
   ```bash
   git push origin feature/your-feature-name
   ```

## Special Considerations
- **Redis features**: Always test with `make test-redis`
- **Transaction features**: Run `make test-transaction`
- **Cache features**: Run `make test-cache`
- **Breaking changes**: Update version and changelog
- **New dependencies**: Update `pyproject.toml` and test installation

## Cleanup After Task
```bash
make clean        # Clean build artifacts
make stop-redis   # Stop Redis if running
```

## Documentation Updates
- Update docstrings for new/changed functions
- Update README.md if API changes
- Update examples if usage patterns change
- Update CHANGELOG.md for significant changes