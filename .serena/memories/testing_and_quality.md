# Zenoo-RPC Testing & Quality Assurance

## Testing Framework
- **Primary**: pytest with async support (pytest-asyncio)
- **Coverage**: pytest-cov for coverage reporting
- **Mocking**: AsyncMock for async function testing
- **Target coverage**: >90%

## Test Structure
```
tests/
├── conftest.py              # Shared fixtures
├── test_*.py               # Unit tests
├── transaction/            # Transaction-specific tests
├── transport/              # Transport layer tests
├── batch/                  # Batch operation tests
└── helpers/                # Test utilities
```

## Test Categories
1. **Unit tests**: Individual component testing
2. **Integration tests**: Component interaction testing
3. **Cache tests**: Memory and Redis cache testing
4. **Transaction tests**: ACID compliance testing
5. **Batch tests**: Bulk operation testing

## Quality Tools Configuration

### Black (Code Formatting)
- Line length: 88 characters
- Target version: Python 3.9+
- Includes: `.py` and `.pyi` files

### Ruff (Linting)
- Enabled rules: E, W, F, I, B, C4, UP
- Target version: Python 3.9
- Line length: 88 characters

### MyPy (Type Checking)
- Strict mode enabled
- Python version: 3.9
- Disallow untyped definitions
- Warn on unused configs and return any

### Pytest Configuration
- Async mode: auto
- Test paths: `tests/`
- Coverage source: `src/zenoo_rpc`
- Coverage reports: terminal + HTML

## Testing Best Practices
1. **Descriptive names**: Use clear, descriptive test function names
2. **Test both paths**: Test success and failure scenarios
3. **Mock externals**: Mock external dependencies (HTTP calls, Redis)
4. **Async testing**: Use proper async test patterns
5. **Fixtures**: Use pytest fixtures for common setup
6. **Parametrized tests**: Use `@pytest.mark.parametrize` for multiple scenarios

## Pre-commit Hooks
- Black formatting
- Ruff linting
- MyPy type checking
- Test execution

## CI/CD Quality Gates
1. All tests must pass
2. Type checking must pass
3. Linting must pass
4. Coverage must be >90%
5. Code must be formatted with Black

## Redis Testing
- Uses Docker container for Redis tests
- Separate test database (port 6380)
- Automatic setup/teardown via Makefile
- Tests both memory and Redis cache backends