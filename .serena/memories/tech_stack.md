# Zenoo-RPC Tech Stack

## Core Dependencies
- **httpx** (>=0.25.0): Modern async HTTP client for transport layer
- **pydantic** (>=2.0.0): Data validation and type safety
- **typing-extensions** (>=4.0.0): Enhanced type hints support

## Development Dependencies
- **pytest** (>=7.0.0): Testing framework
- **pytest-asyncio** (>=0.21.0): Async testing support
- **pytest-cov** (>=4.0.0): Coverage reporting
- **black** (>=23.0.0): Code formatting
- **ruff** (>=0.1.0): Fast Python linter
- **mypy** (>=1.0.0): Static type checking
- **pre-commit** (>=3.0.0): Git hooks for code quality

## Optional Dependencies
- **redis[hiredis]** (>=5.0.0): Redis caching backend
- **aiohttp** (>=3.8.0): Additional HTTP testing utilities

## Documentation Stack
- **mkdocs** (>=1.5.0): Documentation generator
- **mkdocs-material** (>=9.0.0): Material theme
- **mkdocstrings[python]** (>=0.23.0): API documentation

## Infrastructure
- **Docker**: Redis testing container
- **GitHub Actions**: CI/CD (implied from project structure)
- **Hatch**: Modern Python packaging (build-backend)

## Architecture Patterns
- **Async-first**: All I/O operations use async/await
- **Type-safe**: Extensive use of type hints and Pydantic
- **Modular**: Clean separation of concerns across modules
- **Context managers**: Proper resource management with async context managers