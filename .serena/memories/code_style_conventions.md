# Zenoo-RPC Code Style & Conventions

## Python Style Guidelines
- **PEP 8 compliance**: Follow Python's official style guide
- **Line length**: 88 characters (Black default)
- **Target Python version**: 3.9+
- **Encoding**: UTF-8

## Code Formatting Tools
- **Black**: Automatic code formatting (`black src/ tests/`)
- **Ruff**: Fast linting and import sorting
- **isort**: Import statement organization (integrated with Ruff)

## Type Hints & Documentation
- **Type hints required**: All function signatures must have type hints
- **Strict typing**: MyPy strict mode enabled
- **Docstrings**: Required for all public functions and classes
- **Docstring style**: Google/NumPy style preferred

## Async Code Conventions
- **Async-first**: Use `async/await` syntax consistently
- **Context managers**: Prefer `async with` for resource management
- **Async testing**: Use `AsyncMock` for testing async functions

## Naming Conventions
- **Classes**: PascalCase (e.g., `ZenooClient`, `QueryBuilder`)
- **Functions/methods**: snake_case (e.g., `get_server_version`, `list_databases`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DEFAULT_TIMEOUT`)
- **Private members**: Leading underscore (e.g., `_transport`, `_session`)

## Error Handling
- **Structured exceptions**: Create specific exception types for different conditions
- **Meaningful messages**: Always provide clear, actionable error messages
- **Context information**: Include relevant context in exceptions
- **Exception hierarchy**: Inherit from `ZenooError` base class

## Import Organization
```python
# Standard library imports
import asyncio
from typing import Any, Dict, List, Optional

# Third-party imports
import httpx
from pydantic import BaseModel

# Local imports
from .exceptions import ZenooError
from .transport import AsyncTransport
```

## Code Quality Standards
- **Test coverage**: Aim for >90% coverage
- **Type checking**: Must pass MyPy strict mode
- **Linting**: Must pass Ruff checks
- **Formatting**: Must be formatted with Black

## Architecture Principles
1. **Separation of concerns**: Each module has a single responsibility
2. **Dependency injection**: Use dependency injection for testability
3. **Interface segregation**: Small, focused interfaces
4. **Open/closed principle**: Open for extension, closed for modification