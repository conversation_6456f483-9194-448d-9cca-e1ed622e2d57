# Zenoo-RPC Development Commands

## Setup & Installation
```bash
# Install package in development mode
make install-dev
# or manually:
pip install -e ".[dev,test]"
pip install aioredis  # For Redis cache testing
```

## Testing Commands
```bash
# Run all tests
make test
# or: python -m pytest --tb=short

# Run core functionality tests
make test-core

# Run cache tests
make test-cache

# Run Redis cache tests (requires Docker)
make test-redis

# Run transaction tests
make test-transaction

# Run tests with coverage report
make test-coverage
# or: python -m pytest --cov=src/zenoo_rpc --cov-report=html --cov-report=term
```

## Code Quality Commands
```bash
# Format code
make format
# or manually:
black src/ tests/
isort src/ tests/

# Run linting
make lint
# or manually:
ruff check .
flake8 src/ tests/

# Type checking
make type-check
# or: mypy src/zenoo_rpc
```

## Redis Testing Setup
```bash
# Start Redis test container
make setup-redis

# Check Redis status
make redis-status

# Stop Redis container
make stop-redis

# Clean Redis container and data
make clean-redis
```

## Cleanup Commands
```bash
# Clean build artifacts
make clean

# Clean everything including Redis
make clean-all
```

## Development Workflow
1. **Before coding**: `make install-dev`
2. **During development**: `make format` and `make lint`
3. **Before committing**: `make test` and `make type-check`
4. **For Redis features**: `make setup-redis` then `make test-redis`

## System Commands (macOS/Darwin)
```bash
# File operations
ls -la                    # List files with details
find . -name "*.py"       # Find Python files
grep -r "pattern" src/    # Search in source code
cd src/zenoo_rpc         # Navigate to source

# Git operations
git status               # Check repository status
git add .                # Stage changes
git commit -m "message"  # Commit changes
git push origin main     # Push to remote

# Process management
ps aux | grep python     # Find Python processes
kill -9 <pid>           # Kill process by ID
```

## Package Management
```bash
# Install new dependency
pip install package_name

# Update requirements
pip freeze > requirements.txt

# Check outdated packages
pip list --outdated
```