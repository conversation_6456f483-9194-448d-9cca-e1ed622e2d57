# Zenoo-RPC Project Overview

**Zenoo-RPC** (formerly OdooFlow) is a modern, async-first Python library designed to replace `odoorpc` with superior performance, type safety, and developer experience.

## Key Information
- **Version**: 0.3.0 (Alpha stage)
- **Author**: <PERSON><PERSON> <PERSON><PERSON> (<EMAIL>)
- **License**: MIT
- **Python Support**: 3.9+
- **Repository**: https://github.com/tuanle96/zenoo-rpc

## Purpose
Replace the legacy `odoorpc` library with a modern, async-first alternative that provides:
- Type safety with Pydantic models
- Superior developer experience (DX)
- Better performance through async operations
- Intelligent caching and batch operations
- Transaction management with ACID compliance
- Structured exception handling

## Development Status
- ✅ **Phase 1**: Core transport layer and async client (COMPLETED)
- ✅ **Phase 2**: Pydantic models and query builder foundation (COMPLETED)  
- 🔄 **Phase 3**: Advanced features (caching, transactions, batch ops) - IN PROGRESS
- ⏳ **Phase 4**: Documentation and community adoption

## Target Users
Python developers working with Odoo who need:
- Modern async/await patterns
- Type safety and IDE support
- High-performance RPC operations
- Better error handling and debugging