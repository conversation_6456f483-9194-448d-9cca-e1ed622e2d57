#!/usr/bin/env python3
"""
Simple Redis connection test to debug TimeoutError issue.
"""

import asyncio
import sys

async def test_redis_connection():
    """Test basic Redis connection."""
    try:
        import aioredis
        print("✅ aioredis imported successfully")
        
        # Connect to Redis (support both aioredis 1.x and 2.x)
        if hasattr(aioredis, 'from_url'):
            # aioredis 2.x
            redis = aioredis.from_url("redis://localhost:6381/0")
        else:
            # aioredis 1.x
            redis = await aioredis.create_redis_pool("redis://localhost:6381/0")
        print("✅ Redis client created")
        
        # Test ping
        result = await redis.ping()
        print(f"✅ Redis ping result: {result}")
        
        # Test set/get
        await redis.set("test_key", "test_value")
        value = await redis.get("test_key")
        print(f"✅ Redis set/get test: {value}")
        
        # Cleanup
        await redis.delete("test_key")

        # Close connection (support both versions)
        if hasattr(redis, 'wait_closed'):
            # aioredis 1.x
            redis.close()
            await redis.wait_closed()
        else:
            # aioredis 2.x
            await redis.close()

        print("✅ Redis connection test successful!")
        
    except Exception as e:
        print(f"❌ Redis connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_redis_connection())
    sys.exit(0 if success else 1)
