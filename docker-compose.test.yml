version: '3.8'

services:
  redis-test:
    image: redis:7-alpine
    container_name: odooflow-redis-test
    ports:
      - "6381:6379"  # Map to port 6381 to avoid conflicts
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_test_data:/data
    environment:
      - REDIS_REPLICATION_MODE=master
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - odooflow-test

networks:
  odooflow-test:
    driver: bridge

volumes:
  redis_test_data:
    driver: local
