# Zenoo-RPC Documentation

Welcome to the Zenoo-RPC documentation! This directory contains comprehensive guides and references for using Zenoo-RPC.

## 📚 Documentation Structure

### Getting Started
- [Installation Guide](installation.md) - How to install and set up Zenoo-RPC
- [Quick Start](quickstart.md) - Your first steps with Zenoo-RPC
- [Migration from odoorpc](migration.md) - Guide for migrating from odoorpc

### User Guide
- [Client Usage](client.md) - Using the Zenoo-RPCClient
- [Authentication](authentication.md) - Different authentication methods
- [Error Handling](errors.md) - Understanding and handling exceptions
- [Best Practices](best-practices.md) - Recommended patterns and practices

### Advanced Topics
- [Architecture](architecture.md) - Understanding Zenoo-RPC's architecture
- [Performance](performance.md) - Performance optimization tips
- [Extending Zenoo-RPC](extending.md) - How to extend and customize Zenoo-RPC

### API Reference
- [Client API](api/client.md) - Complete client API reference
- [Exceptions](api/exceptions.md) - Exception hierarchy reference
- [Transport](api/transport.md) - Transport layer reference

### Examples
- [Basic Usage](../examples/basic_usage.py) - Basic operations example
- [Advanced Patterns](examples/advanced.md) - Advanced usage patterns
- [Real-world Examples](examples/real-world.md) - Production use cases

## 🚀 Quick Links

- **[Installation](installation.md)** - Get started quickly
- **[API Reference](api/)** - Complete API documentation
- **[Examples](../examples/)** - Code examples and tutorials
- **[Contributing](../CONTRIBUTING.md)** - How to contribute to Zenoo-RPC

## 📖 About This Documentation

This documentation is built with MkDocs and hosted on GitHub Pages. It's designed to be:

- **Comprehensive**: Covers all aspects of Zenoo-RPC
- **Practical**: Includes real-world examples and use cases
- **Up-to-date**: Maintained alongside the codebase
- **Searchable**: Easy to find what you're looking for

## 🤝 Contributing to Documentation

We welcome contributions to improve the documentation! Please see our [Contributing Guide](../CONTRIBUTING.md) for details on how to help.

### Documentation Standards

- Use clear, concise language
- Include code examples where appropriate
- Keep examples up-to-date with the latest API
- Follow the existing structure and style
- Test all code examples

## 📝 Feedback

If you find any issues with the documentation or have suggestions for improvement, please:

1. Open an issue on GitHub
2. Submit a pull request with improvements
3. Start a discussion in GitHub Discussions

Your feedback helps make Zenoo-RPC better for everyone!
