#!/usr/bin/env python3
"""Debug script for batch error handling."""

import asyncio
from unittest.mock import AsyncMock

from src.zenoo_rpc.batch.executor import BatchExecutor
from src.zenoo_rpc.batch.operations import CreateOperation

async def test_batch_executor_error_handling():
    """Test error handling in batch execution."""
    try:
        mock_client = AsyncMock()
        
        # Mock execute_kw to fail on second call
        mock_client.execute_kw.side_effect = [
            [1, 2, 3],  # First operation succeeds
            Exception("Database error"),  # Second operation fails
            [7, 8, 9],  # Third operation succeeds
        ]
        
        executor = BatchExecutor(
            client=mock_client,
            max_chunk_size=10,
            max_concurrency=3
        )
        
        # Create operations
        operations = [
            CreateOperation(
                model="res.partner",
                data=[{"name": f"Partner {i}"} for i in range(1, 4)]
            ),
            CreateOperation(
                model="res.partner",
                data=[{"name": f"Partner {i}"} for i in range(4, 7)]
            ),
            CreateOperation(
                model="res.partner",
                data=[{"name": f"Partner {i}"} for i in range(7, 10)]
            ),
        ]
        
        # Execute operations
        result = await executor.execute_operations(operations)
        
        print(f"Total operations: {result['stats']['total_operations']}")
        print(f"Completed operations: {result['stats']['completed_operations']}")
        print(f"Failed operations: {result['stats']['failed_operations']}")
        
        # Check individual results
        successful_results = [r for r in result["results"] if r["success"]]
        failed_results = [r for r in result["results"] if not r["success"]]
        
        print(f"Successful results: {len(successful_results)}")
        print(f"Failed results: {len(failed_results)}")
        
        for i, res in enumerate(result["results"]):
            print(f"Result {i}: success={res['success']}, error={res.get('error', 'None')}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_batch_executor_error_handling())
