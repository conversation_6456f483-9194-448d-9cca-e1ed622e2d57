{"meta": {"format": 3, "version": "7.8.2", "timestamp": "2025-07-26T15:49:31.562031", "branch_coverage": false, "show_contexts": false}, "files": {"src/zenoo_rpc/__init__.py": {"executed_lines": [1, 17, 18, 28, 29, 30, 33, 39, 40, 41, 43], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 17, 18, 28, 29, 30, 33, 39, 40, 41, 43], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 17, 18, 28, 29, 30, 33, 39, 40, 41, 43], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/batch/__init__.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/batch/context.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 16, 17, 46, 53, 55, 56, 59, 60, 70, 71, 121, 122, 134, 166, 177, 188, 196, 204, 256, 268, 269, 287, 293, 301, 310, 331, 339, 347], "summary": {"covered_lines": 31, "num_statements": 104, "percent_covered": 29.807692307692307, "percent_covered_display": "30", "missing_lines": 73, "excluded_lines": 0}, "missing_lines": [62, 64, 65, 66, 67, 99, 107, 108, 111, 112, 114, 115, 116, 117, 118, 151, 152, 153, 154, 155, 158, 159, 160, 163, 164, 172, 173, 175, 183, 184, 186, 194, 202, 213, 214, 216, 217, 220, 225, 226, 227, 233, 235, 241, 243, 250, 251, 253, 254, 262, 263, 265, 289, 290, 291, 299, 307, 308, 316, 317, 320, 321, 322, 323, 325, 326, 327, 328, 329, 337, 345, 349, 350], "excluded_lines": [], "functions": {"batch_context": {"executed_lines": [46, 53, 55, 56, 59, 60], "summary": {"covered_lines": 6, "num_statements": 11, "percent_covered": 54.54545454545455, "percent_covered_display": "55", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [62, 64, 65, 66, 67], "excluded_lines": []}, "batch_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [99, 107, 108, 111, 112, 114, 115, 116, 117, 118], "excluded_lines": []}, "BatchOperationCollector.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [151, 152, 153, 154, 155, 158, 159, 160, 163, 164], "excluded_lines": []}, "BatchOperationCollector.add": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [172, 173, 175], "excluded_lines": []}, "BatchOperationCollector.add_many": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [183, 184, 186], "excluded_lines": []}, "BatchOperationCollector.has_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [194], "excluded_lines": []}, "BatchOperationCollector.get_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [202], "excluded_lines": []}, "BatchOperationCollector.execute": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [213, 214, 216, 217, 220, 225, 226, 227, 233, 235, 241, 243, 250, 251, 253, 254], "excluded_lines": []}, "BatchOperationCollector.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [262, 263, 265], "excluded_lines": []}, "BatchProgressTracker.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [289, 290, 291], "excluded_lines": []}, "BatchProgressTracker.add_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [299], "excluded_lines": []}, "BatchProgressTracker.remove_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [307, 308], "excluded_lines": []}, "BatchProgressTracker.callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [316, 317, 320, 321, 322, 323, 325, 326, 327, 328, 329], "excluded_lines": []}, "BatchProgressTracker.get_current_progress": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [337], "excluded_lines": []}, "BatchProgressTracker.get_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [345], "excluded_lines": []}, "BatchProgressTracker.clear_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [349, 350], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 16, 17, 70, 71, 121, 122, 134, 166, 177, 188, 196, 204, 256, 268, 269, 287, 293, 301, 310, 331, 339, 347], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BatchOperationCollector": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [151, 152, 153, 154, 155, 158, 159, 160, 163, 164, 172, 173, 175, 183, 184, 186, 194, 202, 213, 214, 216, 217, 220, 225, 226, 227, 233, 235, 241, 243, 250, 251, 253, 254, 262, 263, 265], "excluded_lines": []}, "BatchProgressTracker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [289, 290, 291, 299, 307, 308, 316, 317, 320, 321, 322, 323, 325, 326, 327, 328, 329, 337, 345, 349, 350], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 16, 17, 46, 53, 55, 56, 59, 60, 70, 71, 121, 122, 134, 166, 177, 188, 196, 204, 256, 268, 269, 287, 293, 301, 310, 331, 339, 347], "summary": {"covered_lines": 31, "num_statements": 46, "percent_covered": 67.3913043478261, "percent_covered_display": "67", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [62, 64, 65, 66, 67, 99, 107, 108, 111, 112, 114, 115, 116, 117, 118], "excluded_lines": []}}}, "src/zenoo_rpc/batch/exceptions.py": {"executed_lines": [1, 5, 8, 9, 11, 12, 13, 16, 17, 19, 26, 31, 32, 34, 35, 36, 39, 40, 41, 44, 45, 46], "summary": {"covered_lines": 16, "num_statements": 18, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [27, 28], "excluded_lines": [], "functions": {"BatchError.__init__": {"executed_lines": [12, 13], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchExecutionError.__init__": {"executed_lines": [26], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [27, 28], "excluded_lines": []}, "BatchValidationError.__init__": {"executed_lines": [35, 36], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 11, 16, 17, 19, 31, 32, 34, 39, 40, 41, 44, 45, 46], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BatchError": {"executed_lines": [12, 13], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchExecutionError": {"executed_lines": [26], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [27, 28], "excluded_lines": []}, "BatchValidationError": {"executed_lines": [35, 36], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchSizeError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchTimeoutError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 11, 16, 17, 19, 31, 32, 34, 39, 40, 41, 44, 45, 46], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/batch/executor.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 16, 19, 20, 37, 54, 55, 56, 57, 58, 61, 62, 72, 89, 93, 94, 95, 97, 99, 101, 104, 109, 110, 112, 114, 125, 134, 136, 137, 139, 140, 141, 143, 145, 146, 148, 163, 165, 166, 167, 170, 176, 177, 179, 180, 181, 182, 183, 186, 187, 188, 193, 194, 200, 211, 213, 222, 223, 224, 226, 228, 234, 237, 238, 239, 241, 274, 286, 287, 288, 289, 290, 291, 295, 304, 307, 309, 317, 318, 322, 324, 326, 327, 328, 329, 335, 336, 337, 340, 342, 351, 353, 355, 357, 363, 386, 395, 397, 404, 406], "summary": {"covered_lines": 104, "num_statements": 145, "percent_covered": 71.72413793103448, "percent_covered_display": "72", "missing_lines": 41, "excluded_lines": 0}, "missing_lines": [90, 120, 121, 122, 123, 190, 202, 203, 204, 205, 229, 251, 252, 253, 254, 256, 258, 259, 260, 261, 263, 264, 293, 320, 367, 369, 370, 371, 372, 373, 379, 380, 381, 384, 412, 414, 415, 418, 419, 420, 422], "excluded_lines": [], "functions": {"BatchExecutor.__init__": {"executed_lines": [54, 55, 56, 57, 58, 61, 62], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchExecutor.execute_operations": {"executed_lines": [89, 93, 94, 95, 97, 99, 101, 104, 109, 110, 112, 114], "summary": {"covered_lines": 12, "num_statements": 17, "percent_covered": 70.58823529411765, "percent_covered_display": "71", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [90, 120, 121, 122, 123], "excluded_lines": []}, "BatchExecutor._chunk_operations": {"executed_lines": [134, 136, 137, 139, 140, 141, 143, 145, 146], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchExecutor._execute_chunked_operations": {"executed_lines": [163, 165, 170, 176, 177, 179, 180, 181, 182, 183, 186, 187, 188, 193, 194, 200, 211], "summary": {"covered_lines": 17, "num_statements": 22, "percent_covered": 77.27272727272727, "percent_covered_display": "77", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [190, 202, 203, 204, 205], "excluded_lines": []}, "BatchExecutor._execute_chunked_operations.execute_with_semaphore": {"executed_lines": [166, 167], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchExecutor._execute_single_operation": {"executed_lines": [222, 223, 224, 226, 228, 234, 237, 238, 239, 241], "summary": {"covered_lines": 10, "num_statements": 22, "percent_covered": 45.45454545454545, "percent_covered_display": "45", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [229, 251, 252, 253, 254, 256, 258, 259, 260, 261, 263, 264], "excluded_lines": []}, "BatchExecutor._perform_operation": {"executed_lines": [286, 287, 288, 289, 290, 291], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [293], "excluded_lines": []}, "BatchExecutor._perform_create": {"executed_lines": [304, 307, 309, 317, 318, 322, 324, 326, 327, 328, 329, 335, 336, 337, 340], "summary": {"covered_lines": 15, "num_statements": 16, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [320], "excluded_lines": []}, "BatchExecutor._perform_update": {"executed_lines": [351, 353, 355, 357, 363], "summary": {"covered_lines": 5, "num_statements": 15, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [367, 369, 370, 371, 372, 373, 379, 380, 381, 384], "excluded_lines": []}, "BatchExecutor._perform_delete": {"executed_lines": [395, 397, 404], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchExecutor.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [412, 414, 415, 418, 419, 420, 422], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 16, 19, 20, 37, 72, 125, 148, 213, 274, 295, 342, 386, 406], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BatchExecutor": {"executed_lines": [54, 55, 56, 57, 58, 61, 62, 89, 93, 94, 95, 97, 99, 101, 104, 109, 110, 112, 114, 134, 136, 137, 139, 140, 141, 143, 145, 146, 163, 165, 166, 167, 170, 176, 177, 179, 180, 181, 182, 183, 186, 187, 188, 193, 194, 200, 211, 222, 223, 224, 226, 228, 234, 237, 238, 239, 241, 286, 287, 288, 289, 290, 291, 304, 307, 309, 317, 318, 322, 324, 326, 327, 328, 329, 335, 336, 337, 340, 351, 353, 355, 357, 363, 395, 397, 404], "summary": {"covered_lines": 86, "num_statements": 127, "percent_covered": 67.71653543307086, "percent_covered_display": "68", "missing_lines": 41, "excluded_lines": 0}, "missing_lines": [90, 120, 121, 122, 123, 190, 202, 203, 204, 205, 229, 251, 252, 253, 254, 256, 258, 259, 260, 261, 263, 264, 293, 320, 367, 369, 370, 371, 372, 373, 379, 380, 381, 384, 412, 414, 415, 418, 419, 420, 422], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 16, 19, 20, 37, 72, 125, 148, 213, 274, 295, 342, 386, 406], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/batch/manager.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 18, 19, 21, 24, 25, 27, 28, 29, 30, 36, 38, 39, 40, 42, 44, 45, 46, 48, 50, 51, 52, 54, 56, 59, 60, 87, 102, 103, 104, 105, 108, 111, 119, 128, 129, 130, 132, 133, 135, 150, 153, 161, 164, 165, 167, 169, 170, 176, 177, 178, 180, 181, 183, 192, 210, 216, 223, 225, 226, 230, 250, 257, 264, 266, 267, 271, 289, 295, 302, 304, 305, 309, 320, 329, 330, 342, 349, 350, 351, 352, 353, 355, 373, 379, 381, 382, 384, 404, 411, 413, 414, 416, 434, 440, 442, 443, 445, 458, 473, 476, 479, 481, 483, 486, 490, 493, 494, 498, 501, 503, 504, 512, 518, 520, 526, 528], "summary": {"covered_lines": 119, "num_statements": 143, "percent_covered": 83.21678321678321, "percent_covered_display": "83", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [188, 189, 190, 228, 269, 307, 318, 326, 454, 455, 456, 474, 477, 496, 506, 507, 508, 509, 510, 534, 535, 537, 538, 539], "excluded_lines": [], "functions": {"BatchContext.__init__": {"executed_lines": [28, 29, 30], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchContext.create": {"executed_lines": [38, 39, 40], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchContext.update": {"executed_lines": [44, 45, 46], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchContext.delete": {"executed_lines": [50, 51, 52], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchContext.get_stats": {"executed_lines": [56], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchManager.__init__": {"executed_lines": [102, 103, 104, 105, 108, 111], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchManager.create_batch": {"executed_lines": [128, 129, 130, 132, 133], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchManager.execute_operations": {"executed_lines": [150, 153, 161, 164, 165, 167], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchManager.batch": {"executed_lines": [176, 177, 178, 180, 181, 183], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [188, 189, 190], "excluded_lines": []}, "BatchManager.bulk_create": {"executed_lines": [210, 216, 223, 225, 226], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [228], "excluded_lines": []}, "BatchManager.bulk_update": {"executed_lines": [250, 257, 264, 266, 267], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [269], "excluded_lines": []}, "BatchManager.bulk_delete": {"executed_lines": [289, 295, 302, 304, 305], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [307], "excluded_lines": []}, "BatchManager.get_batch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [318], "excluded_lines": []}, "BatchManager.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [326], "excluded_lines": []}, "Batch.__init__": {"executed_lines": [349, 350, 351, 352, 353], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Batch.create": {"executed_lines": [373, 379, 381, 382], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Batch.update": {"executed_lines": [404, 411, 413, 414], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Batch.delete": {"executed_lines": [434, 440, 442, 443], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Batch.add_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [454, 455, 456], "excluded_lines": []}, "Batch.execute": {"executed_lines": [473, 476, 479, 481, 483, 486, 490, 493, 494, 498, 501, 503, 504], "summary": {"covered_lines": 13, "num_statements": 21, "percent_covered": 61.904761904761905, "percent_covered_display": "62", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [474, 477, 496, 506, 507, 508, 509, 510], "excluded_lines": []}, "Batch.get_operation_count": {"executed_lines": [518], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Batch.get_record_count": {"executed_lines": [526], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Batch.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [534, 535, 537, 538, 539], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 18, 19, 21, 24, 25, 27, 36, 42, 48, 54, 59, 60, 87, 119, 135, 169, 170, 192, 230, 271, 309, 320, 329, 330, 342, 355, 384, 416, 445, 458, 512, 520, 528], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BatchContext": {"executed_lines": [28, 29, 30, 38, 39, 40, 44, 45, 46, 50, 51, 52, 56], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchManager": {"executed_lines": [102, 103, 104, 105, 108, 111, 128, 129, 130, 132, 133, 150, 153, 161, 164, 165, 167, 176, 177, 178, 180, 181, 183, 210, 216, 223, 225, 226, 250, 257, 264, 266, 267, 289, 295, 302, 304, 305], "summary": {"covered_lines": 38, "num_statements": 46, "percent_covered": 82.6086956521739, "percent_covered_display": "83", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [188, 189, 190, 228, 269, 307, 318, 326], "excluded_lines": []}, "Batch": {"executed_lines": [349, 350, 351, 352, 353, 373, 379, 381, 382, 404, 411, 413, 414, 434, 440, 442, 443, 473, 476, 479, 481, 483, 486, 490, 493, 494, 498, 501, 503, 504, 518, 526], "summary": {"covered_lines": 32, "num_statements": 48, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [454, 455, 456, 474, 477, 496, 506, 507, 508, 509, 510, 534, 535, 537, 538, 539], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 18, 19, 21, 24, 25, 27, 36, 42, 48, 54, 59, 60, 87, 119, 135, 169, 170, 192, 230, 271, 309, 320, 329, 330, 342, 355, 384, 416, 445, 458, 512, 520, 528], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/batch/operations.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 34, 35, 36, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 55, 57, 59, 61, 62, 66, 67, 71, 72, 76, 78, 80, 84, 86, 87, 88, 91, 92, 93, 108, 109, 111, 113, 114, 116, 119, 123, 124, 127, 130, 132, 134, 136, 139, 140, 141, 142, 149, 151, 154, 155, 156, 179, 180, 182, 184, 187, 189, 192, 195, 198, 200, 203, 204, 207, 210, 216, 218, 219, 221, 223, 225, 227, 230, 231, 232, 233, 240, 242, 246, 249, 250, 251, 252, 258, 260, 263, 264, 265, 277, 279, 281, 284, 287, 291, 292, 295, 297, 299, 301, 304, 305, 306, 307, 313, 315, 318, 338, 340, 341, 346, 347, 362, 371, 374, 375, 376, 381, 384, 394, 395, 396, 397, 398, 401, 403, 404, 406, 408], "summary": {"covered_lines": 146, "num_statements": 177, "percent_covered": 82.48587570621469, "percent_covered_display": "82", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [64, 69, 74, 82, 117, 120, 125, 128, 137, 185, 190, 193, 196, 201, 205, 208, 211, 214, 228, 247, 282, 285, 288, 293, 302, 352, 353, 359, 372, 377, 378], "excluded_lines": [], "functions": {"BatchOperation.__post_init__": {"executed_lines": [59], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchOperation.validate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [64], "excluded_lines": []}, "BatchOperation.get_batch_size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [69], "excluded_lines": []}, "BatchOperation.split": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [74], "excluded_lines": []}, "BatchOperation.is_completed": {"executed_lines": [78], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchOperation.is_successful": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [82], "excluded_lines": []}, "BatchOperation.get_duration": {"executed_lines": [86, 87, 88], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CreateOperation.validate": {"executed_lines": [113, 114, 116, 119, 123, 124, 127], "summary": {"covered_lines": 7, "num_statements": 11, "percent_covered": 63.63636363636363, "percent_covered_display": "64", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [117, 120, 125, 128], "excluded_lines": []}, "CreateOperation.get_batch_size": {"executed_lines": [132], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CreateOperation.split": {"executed_lines": [136, 139, 140, 141, 142, 149, 151], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [137], "excluded_lines": []}, "UpdateOperation.validate": {"executed_lines": [184, 187, 189, 192, 195, 198, 200, 203, 204, 207, 210], "summary": {"covered_lines": 11, "num_statements": 20, "percent_covered": 55.0, "percent_covered_display": "55", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [185, 190, 193, 196, 201, 205, 208, 211, 214], "excluded_lines": []}, "UpdateOperation.get_batch_size": {"executed_lines": [218, 219, 221], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UpdateOperation.split": {"executed_lines": [225, 227, 230, 231, 232, 233, 240, 242, 246, 249, 250, 251, 252, 258, 260], "summary": {"covered_lines": 15, "num_statements": 17, "percent_covered": 88.23529411764706, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [228, 247], "excluded_lines": []}, "DeleteOperation.validate": {"executed_lines": [281, 284, 287, 291, 292], "summary": {"covered_lines": 5, "num_statements": 9, "percent_covered": 55.55555555555556, "percent_covered_display": "56", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [282, 285, 288, 293], "excluded_lines": []}, "DeleteOperation.get_batch_size": {"executed_lines": [297], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DeleteOperation.split": {"executed_lines": [301, 304, 305, 306, 307, 313, 315], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [302], "excluded_lines": []}, "create_batch_operation": {"executed_lines": [338, 340, 341, 346, 347], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [352, 353, 359], "excluded_lines": []}, "validate_batch_operations": {"executed_lines": [371, 374, 375, 376, 381], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [372, 377, 378], "excluded_lines": []}, "_check_operation_conflicts": {"executed_lines": [394, 395, 396, 397, 398, 401, 403, 404, 406, 408], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 34, 35, 36, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 55, 57, 61, 62, 66, 67, 71, 72, 76, 80, 84, 91, 92, 93, 108, 109, 111, 130, 134, 154, 155, 156, 179, 180, 182, 216, 223, 263, 264, 265, 277, 279, 295, 299, 318, 362, 384], "summary": {"covered_lines": 64, "num_statements": 64, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"OperationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OperationStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BatchOperation": {"executed_lines": [59, 78, 86, 87, 88], "summary": {"covered_lines": 5, "num_statements": 9, "percent_covered": 55.55555555555556, "percent_covered_display": "56", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [64, 69, 74, 82], "excluded_lines": []}, "CreateOperation": {"executed_lines": [113, 114, 116, 119, 123, 124, 127, 132, 136, 139, 140, 141, 142, 149, 151], "summary": {"covered_lines": 15, "num_statements": 20, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [117, 120, 125, 128, 137], "excluded_lines": []}, "UpdateOperation": {"executed_lines": [184, 187, 189, 192, 195, 198, 200, 203, 204, 207, 210, 218, 219, 221, 225, 227, 230, 231, 232, 233, 240, 242, 246, 249, 250, 251, 252, 258, 260], "summary": {"covered_lines": 29, "num_statements": 40, "percent_covered": 72.5, "percent_covered_display": "72", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [185, 190, 193, 196, 201, 205, 208, 211, 214, 228, 247], "excluded_lines": []}, "DeleteOperation": {"executed_lines": [281, 284, 287, 291, 292, 297, 301, 304, 305, 306, 307, 313, 315], "summary": {"covered_lines": 13, "num_statements": 18, "percent_covered": 72.22222222222223, "percent_covered_display": "72", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [282, 285, 288, 293, 302], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 34, 35, 36, 42, 43, 44, 45, 46, 47, 48, 51, 52, 53, 54, 55, 57, 61, 62, 66, 67, 71, 72, 76, 80, 84, 91, 92, 93, 108, 109, 111, 130, 134, 154, 155, 156, 179, 180, 182, 216, 223, 263, 264, 265, 277, 279, 295, 299, 318, 338, 340, 341, 346, 347, 362, 371, 374, 375, 376, 381, 384, 394, 395, 396, 397, 398, 401, 403, 404, 406, 408], "summary": {"covered_lines": 84, "num_statements": 90, "percent_covered": 93.33333333333333, "percent_covered_display": "93", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [352, 353, 359, 372, 377, 378], "excluded_lines": []}}}, "src/zenoo_rpc/cache/__init__.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/cache/backends.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 30, 31, 42, 43, 61, 62, 73, 74, 85, 86, 94, 95, 104, 105, 122, 135, 136, 137, 140, 141, 144, 145, 146, 147, 150, 151, 154, 156, 158, 159, 161, 163, 164, 165, 167, 169, 172, 188, 190, 192, 193, 194, 196, 198, 200, 202, 203, 204, 207, 208, 210, 211, 212, 213, 216, 217, 219, 220, 222, 229, 231, 233, 236, 239, 240, 241, 242, 246, 247, 249, 251, 253, 254, 255, 256, 258, 259, 261, 263, 265, 266, 268, 270, 271, 272, 273, 275, 277, 278, 279, 281, 293, 303, 304, 323, 340, 341, 342, 343, 344, 347, 348, 351, 352, 353, 354, 355, 357, 359, 361, 364, 374, 381, 382, 384, 394, 396, 398, 400, 401, 402, 410, 412, 413, 414, 422, 424, 427, 429, 430, 431, 433, 434, 435, 436, 437, 439, 440, 441, 448, 455, 456, 457, 459, 460, 462, 463, 467, 468, 475, 477, 478, 479, 481, 482, 483, 484, 485, 493, 495, 496, 497, 499, 500, 501, 508, 527, 529, 530, 532, 546, 547, 548, 549, 557, 559, 561, 562, 564, 566, 567, 574], "summary": {"covered_lines": 192, "num_statements": 266, "percent_covered": 72.18045112781955, "percent_covered_display": "72", "missing_lines": 74, "excluded_lines": 0}, "missing_lines": [40, 59, 71, 83, 92, 101, 166, 168, 170, 174, 175, 176, 181, 182, 183, 185, 186, 244, 295, 296, 297, 298, 299, 300, 366, 386, 387, 391, 392, 403, 404, 406, 407, 408, 415, 416, 418, 419, 420, 425, 443, 444, 445, 446, 465, 470, 471, 472, 473, 486, 488, 489, 490, 491, 503, 504, 505, 506, 510, 512, 514, 515, 517, 518, 520, 522, 523, 524, 525, 554, 555, 570, 571, 572], "excluded_lines": [], "functions": {"CacheBackend.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [40], "excluded_lines": []}, "CacheBackend.set": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [59], "excluded_lines": []}, "CacheBackend.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [71], "excluded_lines": []}, "CacheBackend.exists": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [83], "excluded_lines": []}, "CacheBackend.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [92], "excluded_lines": []}, "CacheBackend.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [101], "excluded_lines": []}, "MemoryCache.__init__": {"executed_lines": [135, 136, 137, 140, 141, 144, 145, 146, 147, 150, 151, 154], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache._start_cleanup_task": {"executed_lines": [158, 159], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache._cleanup_expired": {"executed_lines": [163, 164, 165, 167, 169], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [166, 168, 170], "excluded_lines": []}, "MemoryCache._remove_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [174, 175, 176, 181, 182, 183, 185, 186], "excluded_lines": []}, "MemoryCache._evict_lru": {"executed_lines": [190, 192, 193, 194], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache.get": {"executed_lines": [198, 200, 202, 203, 204, 207, 208, 210, 211, 212, 213, 216, 217, 219, 220], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache.set": {"executed_lines": [229, 231, 233, 236, 239, 240, 241, 242, 246, 247], "summary": {"covered_lines": 10, "num_statements": 11, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [244], "excluded_lines": []}, "MemoryCache.delete": {"executed_lines": [251, 253, 254, 255, 256, 258, 259, 261], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache.exists": {"executed_lines": [265, 266], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache.clear": {"executed_lines": [270, 271, 272, 273], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache.get_stats": {"executed_lines": [277, 278, 279, 281], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryCache.close": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [295, 296, 297, 298, 299, 300], "excluded_lines": []}, "RedisCache.__init__": {"executed_lines": [340, 341, 342, 343, 344, 347, 348, 351, 352, 353, 354, 355], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RedisCache.connect": {"executed_lines": [359, 361, 364, 374, 381, 382, 384], "summary": {"covered_lines": 7, "num_statements": 12, "percent_covered": 58.333333333333336, "percent_covered_display": "58", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [366, 386, 387, 391, 392], "excluded_lines": []}, "RedisCache._make_key": {"executed_lines": [396], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RedisCache._serialize": {"executed_lines": [400, 401, 402], "summary": {"covered_lines": 3, "num_statements": 8, "percent_covered": 37.5, "percent_covered_display": "38", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [403, 404, 406, 407, 408], "excluded_lines": []}, "RedisCache._deserialize": {"executed_lines": [412, 413, 414], "summary": {"covered_lines": 3, "num_statements": 8, "percent_covered": 37.5, "percent_covered_display": "38", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [415, 416, 418, 419, 420], "excluded_lines": []}, "RedisCache._ensure_connected": {"executed_lines": [424], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [425], "excluded_lines": []}, "RedisCache.get": {"executed_lines": [429, 430, 431, 433, 434, 435, 436, 437, 439, 440, 441], "summary": {"covered_lines": 11, "num_statements": 15, "percent_covered": 73.33333333333333, "percent_covered_display": "73", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [443, 444, 445, 446], "excluded_lines": []}, "RedisCache.set": {"executed_lines": [455, 456, 457, 459, 460, 462, 463, 467, 468], "summary": {"covered_lines": 9, "num_statements": 14, "percent_covered": 64.28571428571429, "percent_covered_display": "64", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [465, 470, 471, 472, 473], "excluded_lines": []}, "RedisCache.delete": {"executed_lines": [477, 478, 479, 481, 482, 483, 484, 485], "summary": {"covered_lines": 8, "num_statements": 13, "percent_covered": 61.53846153846154, "percent_covered_display": "62", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [486, 488, 489, 490, 491], "excluded_lines": []}, "RedisCache.exists": {"executed_lines": [495, 496, 497, 499, 500, 501], "summary": {"covered_lines": 6, "num_statements": 10, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [503, 504, 505, 506], "excluded_lines": []}, "RedisCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [510, 512, 514, 515, 517, 518, 520, 522, 523, 524, 525], "excluded_lines": []}, "RedisCache.get_stats": {"executed_lines": [529, 530, 532, 546, 547, 548, 549, 557], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [554, 555], "excluded_lines": []}, "RedisCache.close": {"executed_lines": [561, 562, 564, 566, 567, 574], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [570, 571, 572], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 30, 31, 42, 43, 61, 62, 73, 74, 85, 86, 94, 95, 104, 105, 122, 156, 161, 172, 188, 196, 222, 249, 263, 268, 275, 293, 303, 304, 323, 357, 394, 398, 410, 422, 427, 448, 475, 493, 508, 527, 559], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheBackend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [40, 59, 71, 83, 92, 101], "excluded_lines": []}, "MemoryCache": {"executed_lines": [135, 136, 137, 140, 141, 144, 145, 146, 147, 150, 151, 154, 158, 159, 163, 164, 165, 167, 169, 190, 192, 193, 194, 198, 200, 202, 203, 204, 207, 208, 210, 211, 212, 213, 216, 217, 219, 220, 229, 231, 233, 236, 239, 240, 241, 242, 246, 247, 251, 253, 254, 255, 256, 258, 259, 261, 265, 266, 270, 271, 272, 273, 277, 278, 279, 281], "summary": {"covered_lines": 66, "num_statements": 84, "percent_covered": 78.57142857142857, "percent_covered_display": "79", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [166, 168, 170, 174, 175, 176, 181, 182, 183, 185, 186, 244, 295, 296, 297, 298, 299, 300], "excluded_lines": []}, "RedisCache": {"executed_lines": [340, 341, 342, 343, 344, 347, 348, 351, 352, 353, 354, 355, 359, 361, 364, 374, 381, 382, 384, 396, 400, 401, 402, 412, 413, 414, 424, 429, 430, 431, 433, 434, 435, 436, 437, 439, 440, 441, 455, 456, 457, 459, 460, 462, 463, 467, 468, 477, 478, 479, 481, 482, 483, 484, 485, 495, 496, 497, 499, 500, 501, 529, 530, 532, 546, 547, 548, 549, 557, 561, 562, 564, 566, 567, 574], "summary": {"covered_lines": 75, "num_statements": 125, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 50, "excluded_lines": 0}, "missing_lines": [366, 386, 387, 391, 392, 403, 404, 406, 407, 408, 415, 416, 418, 419, 420, 425, 443, 444, 445, 446, 465, 470, 471, 472, 473, 486, 488, 489, 490, 491, 503, 504, 505, 506, 510, 512, 514, 515, 517, 518, 520, 522, 523, 524, 525, 554, 555, 570, 571, 572], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 30, 31, 42, 43, 61, 62, 73, 74, 85, 86, 94, 95, 104, 105, 122, 156, 161, 172, 188, 196, 222, 249, 263, 268, 275, 293, 303, 304, 323, 357, 394, 398, 410, 422, 427, 448, 475, 493, 508, 527, 559], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/cache/decorators.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 22, 54, 55, 56, 58, 59, 61, 62, 68, 72, 75, 80, 81, 82, 83, 84, 89, 90, 93, 94, 95, 99, 102, 103, 104, 106, 108, 111, 135, 136, 137, 139, 143, 146, 153, 154, 155, 156, 157, 162, 163, 166, 167, 168, 172, 175, 176, 177, 178, 179, 181, 183, 186, 204, 205, 207, 208, 209, 211, 214, 215, 217, 218, 219, 220, 224, 226, 228, 231, 232, 244, 266, 270, 275, 284, 292, 313, 331, 332, 335, 340, 341, 343, 346, 355, 357, 358, 360, 362, 364, 367, 376, 378, 385, 388, 397, 398, 399, 400], "summary": {"covered_lines": 108, "num_statements": 150, "percent_covered": 72.0, "percent_covered_display": "72", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [64, 65, 69, 73, 85, 86, 96, 97, 140, 141, 158, 159, 169, 170, 221, 222, 257, 258, 259, 260, 263, 264, 268, 272, 273, 281, 282, 290, 298, 300, 301, 302, 305, 306, 307, 308, 310, 379, 381, 383, 401, 403], "excluded_lines": [], "functions": {"cached": {"executed_lines": [54, 55, 108], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "cached.decorator": {"executed_lines": [56, 102, 103, 104, 106], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "cached.decorator.wrapper": {"executed_lines": [58, 59, 61, 62, 68, 72, 75, 80, 81, 82, 83, 84, 89, 90, 93, 94, 95, 99], "summary": {"covered_lines": 18, "num_statements": 26, "percent_covered": 69.23076923076923, "percent_covered_display": "69", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [64, 65, 69, 73, 85, 86, 96, 97], "excluded_lines": []}, "cache_result": {"executed_lines": [135, 136, 183], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "cache_result.decorator": {"executed_lines": [137, 175, 176, 177, 178, 179, 181], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "cache_result.decorator.wrapper": {"executed_lines": [139, 143, 146, 153, 154, 155, 156, 157, 162, 163, 166, 167, 168, 172], "summary": {"covered_lines": 14, "num_statements": 20, "percent_covered": 70.0, "percent_covered_display": "70", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [140, 141, 158, 159, 169, 170], "excluded_lines": []}, "invalidate_cache": {"executed_lines": [204, 205, 207, 208, 228], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "invalidate_cache.decorator": {"executed_lines": [209, 226], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "invalidate_cache.decorator.wrapper": {"executed_lines": [211, 214, 215, 217, 218, 219, 220, 224], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [221, 222], "excluded_lines": []}, "CacheInvalidator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [257, 258, 259, 260, 263, 264], "excluded_lines": []}, "CacheInvalidator.__aenter__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [268], "excluded_lines": []}, "CacheInvalidator.__aexit__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [272, 273], "excluded_lines": []}, "CacheInvalidator.add_pattern": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [281, 282], "excluded_lines": []}, "CacheInvalidator.add_model_pattern": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [290], "excluded_lines": []}, "CacheInvalidator.invalidate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [298, 300, 301, 302, 305, 306, 307, 308, 310], "excluded_lines": []}, "_build_function_cache_key": {"executed_lines": [331, 332, 335, 340, 341, 343], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "_serialize_args": {"executed_lines": [355, 357, 358, 360, 362, 364], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "_serialize_kwargs": {"executed_lines": [376, 378, 385], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [379, 381, 383], "excluded_lines": []}, "_hash_data": {"executed_lines": [397, 398, 399, 400], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [401, 403], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 22, 111, 186, 231, 232, 244, 266, 270, 275, 284, 292, 313, 346, 367, 388], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheInvalidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [257, 258, 259, 260, 263, 264, 268, 272, 273, 281, 282, 290, 298, 300, 301, 302, 305, 306, 307, 308, 310], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 22, 54, 55, 56, 58, 59, 61, 62, 68, 72, 75, 80, 81, 82, 83, 84, 89, 90, 93, 94, 95, 99, 102, 103, 104, 106, 108, 111, 135, 136, 137, 139, 143, 146, 153, 154, 155, 156, 157, 162, 163, 166, 167, 168, 172, 175, 176, 177, 178, 179, 181, 183, 186, 204, 205, 207, 208, 209, 211, 214, 215, 217, 218, 219, 220, 224, 226, 228, 231, 232, 244, 266, 270, 275, 284, 292, 313, 331, 332, 335, 340, 341, 343, 346, 355, 357, 358, 360, 362, 364, 367, 376, 378, 385, 388, 397, 398, 399, 400], "summary": {"covered_lines": 108, "num_statements": 129, "percent_covered": 83.72093023255815, "percent_covered_display": "84", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [64, 65, 69, 73, 85, 86, 96, 97, 140, 141, 158, 159, 169, 170, 221, 222, 379, 381, 383, 401, 403], "excluded_lines": []}}}, "src/zenoo_rpc/cache/exceptions.py": {"executed_lines": [1, 5, 8, 9, 10, 13, 14, 16, 17, 18, 21, 22, 23, 26, 27, 29, 34, 35, 36, 39, 40, 41], "summary": {"covered_lines": 15, "num_statements": 17, "percent_covered": 88.23529411764706, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [30, 31], "excluded_lines": [], "functions": {"CacheBackendError.__init__": {"executed_lines": [17, 18], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheSerializationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [30, 31], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 10, 13, 14, 16, 21, 22, 23, 26, 27, 29, 34, 35, 36, 39, 40, 41], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheBackendError": {"executed_lines": [17, 18], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheKeyError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheSerializationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [30, 31], "excluded_lines": []}, "CacheConnectionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheTimeoutError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 10, 13, 14, 16, 21, 22, 23, 26, 27, 29, 34, 35, 36, 39, 40, 41], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/cache/keys.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 16, 17, 18, 31, 32, 33, 34, 35, 37, 44, 45, 48, 49, 52, 53, 55, 57, 59, 63, 71, 72, 76, 93, 111, 142, 145, 149, 152, 153, 154, 157, 159, 168, 190, 193, 195, 197, 201, 203, 212, 235, 244, 246, 254, 263, 265, 268, 271, 277, 289, 290, 291, 292, 296, 299, 303, 304, 305, 308, 311], "summary": {"covered_lines": 64, "num_statements": 93, "percent_covered": 68.81720430107526, "percent_covered_display": "69", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [61, 65, 66, 67, 68, 69, 74, 85, 102, 143, 146, 191, 198, 199, 273, 274, 294, 297, 300, 306, 325, 327, 328, 330, 335, 336, 338, 339, 341], "excluded_lines": [], "functions": {"CacheKey.__post_init__": {"executed_lines": [44, 45, 48, 49, 52, 53], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheKey.__str__": {"executed_lines": [57], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheKey.__hash__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [61], "excluded_lines": []}, "CacheKey.__eq__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [65, 66, 67, 68, 69], "excluded_lines": []}, "CacheKey.full_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [74], "excluded_lines": []}, "CacheKey.with_suffix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [85], "excluded_lines": []}, "CacheKey.with_prefix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [102], "excluded_lines": []}, "make_cache_key": {"executed_lines": [142, 145, 149, 152, 153, 154, 157, 159], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [143, 146], "excluded_lines": []}, "make_model_cache_key": {"executed_lines": [190, 193, 195, 197, 201, 203], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [191, 198, 199], "excluded_lines": []}, "make_query_cache_key": {"executed_lines": [235, 244, 246], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "_hash_params": {"executed_lines": [263, 265, 268, 271], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [273, 274], "excluded_lines": []}, "validate_cache_key": {"executed_lines": [289, 290, 291, 292, 296, 299, 303, 304, 305, 308], "summary": {"covered_lines": 10, "num_statements": 14, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [294, 297, 300, 306], "excluded_lines": []}, "parse_cache_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [325, 327, 328, 330, 335, 336, 338, 339, 341], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 16, 17, 18, 31, 32, 33, 34, 35, 37, 55, 59, 63, 71, 72, 76, 93, 111, 168, 212, 254, 277, 311], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheKey": {"executed_lines": [44, 45, 48, 49, 52, 53, 57], "summary": {"covered_lines": 7, "num_statements": 16, "percent_covered": 43.75, "percent_covered_display": "44", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [61, 65, 66, 67, 68, 69, 74, 85, 102], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 16, 17, 18, 31, 32, 33, 34, 35, 37, 55, 59, 63, 71, 72, 76, 93, 111, 142, 145, 149, 152, 153, 154, 157, 159, 168, 190, 193, 195, 197, 201, 203, 212, 235, 244, 246, 254, 263, 265, 268, 271, 277, 289, 290, 291, 292, 296, 299, 303, 304, 305, 308, 311], "summary": {"covered_lines": 57, "num_statements": 77, "percent_covered": 74.02597402597402, "percent_covered_display": "74", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [143, 146, 191, 198, 199, 273, 274, 294, 297, 300, 306, 325, 327, 328, 330, 335, 336, 338, 339, 341], "excluded_lines": []}}}, "src/zenoo_rpc/cache/manager.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 17, 20, 21, 48, 50, 51, 52, 53, 56, 64, 72, 88, 94, 97, 98, 100, 101, 103, 110, 112, 119, 121, 127, 128, 132, 171, 187, 188, 192, 193, 205, 219, 222, 223, 225, 229, 230, 231, 233, 234, 236, 238, 244, 262, 265, 266, 268, 272, 273, 274, 280, 294, 297, 298, 300, 303, 304, 305, 311, 325, 326, 328, 349, 363, 366, 367, 369, 373, 375, 378, 379, 380, 382, 383, 384, 386, 387, 389, 421, 435, 436, 438, 447, 456, 459, 460, 461, 463, 464, 465, 468, 469, 470, 473, 474, 475, 477, 479, 484, 489, 493, 503, 523, 524, 526, 542, 543, 545, 565, 566, 568, 584, 585], "summary": {"covered_lines": 119, "num_statements": 193, "percent_covered": 61.6580310880829, "percent_covered_display": "62", "missing_lines": 74, "excluded_lines": 0}, "missing_lines": [130, 152, 160, 163, 166, 167, 169, 197, 198, 203, 220, 226, 227, 240, 241, 242, 263, 269, 270, 276, 277, 278, 295, 301, 307, 308, 309, 337, 338, 339, 340, 341, 344, 345, 346, 347, 364, 370, 371, 392, 393, 394, 396, 397, 398, 400, 401, 403, 406, 407, 408, 409, 410, 411, 414, 415, 417, 418, 419, 448, 449, 450, 451, 452, 453, 481, 482, 486, 487, 491, 495, 496, 497, 499], "excluded_lines": [], "functions": {"CacheManager.__init__": {"executed_lines": [50, 51, 52, 53, 56, 64], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.setup_memory_cache": {"executed_lines": [88, 94, 97, 98, 100, 101], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.add_backend": {"executed_lines": [110], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.add_strategy": {"executed_lines": [119], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.set_default_backend": {"executed_lines": [127, 128], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [130], "excluded_lines": []}, "CacheManager.setup_redis_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [152, 160, 163, 166, 167, 169], "excluded_lines": []}, "CacheManager._create_strategy": {"executed_lines": [187, 188, 192, 193], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [197, 198, 203], "excluded_lines": []}, "CacheManager.get": {"executed_lines": [219, 222, 223, 225, 229, 230, 231, 233, 234, 236, 238], "summary": {"covered_lines": 11, "num_statements": 17, "percent_covered": 64.70588235294117, "percent_covered_display": "65", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [220, 226, 227, 240, 241, 242], "excluded_lines": []}, "CacheManager.set": {"executed_lines": [262, 265, 266, 268, 272, 273, 274], "summary": {"covered_lines": 7, "num_statements": 13, "percent_covered": 53.84615384615385, "percent_covered_display": "54", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [263, 269, 270, 276, 277, 278], "excluded_lines": []}, "CacheManager.delete": {"executed_lines": [294, 297, 298, 300, 303, 304, 305], "summary": {"covered_lines": 7, "num_statements": 12, "percent_covered": 58.333333333333336, "percent_covered_display": "58", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [295, 301, 307, 308, 309], "excluded_lines": []}, "CacheManager.exists": {"executed_lines": [325, 326], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [337, 338, 339, 340, 341, 344, 345, 346, 347], "excluded_lines": []}, "CacheManager.invalidate_pattern": {"executed_lines": [363, 366, 367, 369, 373, 375, 378, 379, 380, 382, 383, 384, 386, 387, 389], "summary": {"covered_lines": 15, "num_statements": 38, "percent_covered": 39.473684210526315, "percent_covered_display": "39", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [364, 370, 371, 392, 393, 394, 396, 397, 398, 400, 401, 403, 406, 407, 408, 409, 410, 411, 414, 415, 417, 418, 419], "excluded_lines": []}, "CacheManager.invalidate_model": {"executed_lines": [435, 436], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.get_stats": {"executed_lines": [447, 456, 459, 460, 461, 463, 464, 465, 468, 469, 470, 473, 474, 475, 477], "summary": {"covered_lines": 15, "num_statements": 21, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [448, 449, 450, 451, 452, 453], "excluded_lines": []}, "CacheManager.enable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [481, 482], "excluded_lines": []}, "CacheManager.disable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [486, 487], "excluded_lines": []}, "CacheManager.is_enabled": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [491], "excluded_lines": []}, "CacheManager.close": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [495, 496, 497, 499], "excluded_lines": []}, "CacheManager.cache_query_result": {"executed_lines": [523, 524], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.get_cached_query_result": {"executed_lines": [542, 543], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.cache_model_record": {"executed_lines": [565, 566], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheManager.get_cached_model_record": {"executed_lines": [584, 585], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 17, 20, 21, 48, 72, 103, 112, 121, 132, 171, 205, 244, 280, 311, 328, 349, 421, 438, 479, 484, 489, 493, 503, 526, 545, 568], "summary": {"covered_lines": 32, "num_statements": 32, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheManager": {"executed_lines": [50, 51, 52, 53, 56, 64, 88, 94, 97, 98, 100, 101, 110, 119, 127, 128, 187, 188, 192, 193, 219, 222, 223, 225, 229, 230, 231, 233, 234, 236, 238, 262, 265, 266, 268, 272, 273, 274, 294, 297, 298, 300, 303, 304, 305, 325, 326, 363, 366, 367, 369, 373, 375, 378, 379, 380, 382, 383, 384, 386, 387, 389, 435, 436, 447, 456, 459, 460, 461, 463, 464, 465, 468, 469, 470, 473, 474, 475, 477, 523, 524, 542, 543, 565, 566, 584, 585], "summary": {"covered_lines": 87, "num_statements": 161, "percent_covered": 54.037267080745345, "percent_covered_display": "54", "missing_lines": 74, "excluded_lines": 0}, "missing_lines": [130, 152, 160, 163, 166, 167, 169, 197, 198, 203, 220, 226, 227, 240, 241, 242, 263, 269, 270, 276, 277, 278, 295, 301, 307, 308, 309, 337, 338, 339, 340, 341, 344, 345, 346, 347, 364, 370, 371, 392, 393, 394, 396, 397, 398, 400, 401, 403, 406, 407, 408, 409, 410, 411, 414, 415, 417, 418, 419, 448, 449, 450, 451, 452, 453, 481, 482, 486, 487, 491, 495, 496, 497, 499], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 17, 20, 21, 48, 72, 103, 112, 121, 132, 171, 205, 244, 280, 311, 328, 349, 421, 438, 479, 484, 489, 493, 503, 526, 545, 568], "summary": {"covered_lines": 32, "num_statements": 32, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/cache/strategies.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 18, 19, 25, 31, 33, 34, 38, 39, 48, 49, 53, 54, 58, 59, 64, 65, 83, 96, 97, 98, 101, 102, 104, 106, 107, 109, 111, 113, 116, 117, 130, 132, 133, 136, 137, 138, 141, 143, 144, 145, 146, 148, 156, 157, 160, 161, 164, 166, 168, 171, 173, 175, 180, 182, 185, 186, 191, 198, 201, 202, 220, 231, 232, 235, 237, 239, 242, 243, 245, 247, 249, 250, 251, 253, 255, 257, 259, 261, 263, 265, 272, 275, 277, 279, 282, 284, 286, 295, 300, 314, 315, 333, 346, 347, 348, 351, 352, 353, 355, 357, 360, 362, 364, 366, 367, 380, 382, 384, 386, 387, 389, 391, 393, 395, 397, 399, 401, 408, 411, 413, 415, 416, 419, 421, 423, 432, 437], "summary": {"covered_lines": 130, "num_statements": 166, "percent_covered": 78.3132530120482, "percent_covered_display": "78", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [36, 46, 51, 56, 61, 119, 124, 125, 126, 128, 177, 178, 288, 291, 293, 297, 298, 302, 304, 311, 370, 371, 374, 378, 425, 428, 430, 434, 435, 439, 442, 443, 444, 445, 447, 458], "excluded_lines": [], "functions": {"CacheStrategy.__init__": {"executed_lines": [31], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheStrategy.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [36], "excluded_lines": []}, "CacheStrategy.set": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [46], "excluded_lines": []}, "CacheStrategy.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [51], "excluded_lines": []}, "CacheStrategy.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [56], "excluded_lines": []}, "CacheStrategy.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [61], "excluded_lines": []}, "TTLCache.__init__": {"executed_lines": [96, 97, 98, 101, 102], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TTLCache._is_expired": {"executed_lines": [106, 107, 109], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TTLCache._cleanup_expired": {"executed_lines": [113, 116, 117], "summary": {"covered_lines": 3, "num_statements": 8, "percent_covered": 37.5, "percent_covered_display": "38", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [119, 124, 125, 126, 128], "excluded_lines": []}, "TTLCache.get": {"executed_lines": [132, 133, 136, 137, 138, 141, 143, 144, 145, 146], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TTLCache.set": {"executed_lines": [156, 157, 160, 161, 164], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TTLCache.delete": {"executed_lines": [168, 171, 173], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TTLCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [177, 178], "excluded_lines": []}, "TTLCache.get_stats": {"executed_lines": [182, 185, 186, 191, 198], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LRUCache.__init__": {"executed_lines": [231, 232, 235], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LRUCache._update_access": {"executed_lines": [239, 242, 243], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LRUCache._evict_lru": {"executed_lines": [247, 249, 250, 251], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LRUCache.get": {"executed_lines": [255, 257, 259, 261, 263], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LRUCache.set": {"executed_lines": [272, 275, 277, 279, 282, 284], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LRUCache.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [288, 291, 293], "excluded_lines": []}, "LRUCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [297, 298], "excluded_lines": []}, "LRUCache.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [302, 304, 311], "excluded_lines": []}, "LFUCache.__init__": {"executed_lines": [346, 347, 348, 351, 352, 353], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LFUCache._update_frequency": {"executed_lines": [357, 360], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LFUCache._apply_aging": {"executed_lines": [364, 366, 367], "summary": {"covered_lines": 3, "num_statements": 7, "percent_covered": 42.857142857142854, "percent_covered_display": "43", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [370, 371, 374, 378], "excluded_lines": []}, "LFUCache._evict_lfu": {"executed_lines": [382, 384, 386, 387], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LFUCache.get": {"executed_lines": [391, 393, 395, 397, 399], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LFUCache.set": {"executed_lines": [408, 411, 413, 415, 416, 419, 421], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LFUCache.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [425, 428, 430], "excluded_lines": []}, "LFUCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [434, 435], "excluded_lines": []}, "LFUCache.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [439, 442, 443, 444, 445, 447, 458], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 18, 19, 25, 33, 34, 38, 39, 48, 49, 53, 54, 58, 59, 64, 65, 83, 104, 111, 130, 148, 166, 175, 180, 201, 202, 220, 237, 245, 253, 265, 286, 295, 300, 314, 315, 333, 355, 362, 380, 389, 401, 423, 432, 437], "summary": {"covered_lines": 47, "num_statements": 47, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheStrategy": {"executed_lines": [31], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 16.666666666666668, "percent_covered_display": "17", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [36, 46, 51, 56, 61], "excluded_lines": []}, "TTLCache": {"executed_lines": [96, 97, 98, 101, 102, 106, 107, 109, 113, 116, 117, 132, 133, 136, 137, 138, 141, 143, 144, 145, 146, 156, 157, 160, 161, 164, 168, 171, 173, 182, 185, 186, 191, 198], "summary": {"covered_lines": 34, "num_statements": 41, "percent_covered": 82.92682926829268, "percent_covered_display": "83", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [119, 124, 125, 126, 128, 177, 178], "excluded_lines": []}, "LRUCache": {"executed_lines": [231, 232, 235, 239, 242, 243, 247, 249, 250, 251, 255, 257, 259, 261, 263, 272, 275, 277, 279, 282, 284], "summary": {"covered_lines": 21, "num_statements": 29, "percent_covered": 72.41379310344827, "percent_covered_display": "72", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [288, 291, 293, 297, 298, 302, 304, 311], "excluded_lines": []}, "LFUCache": {"executed_lines": [346, 347, 348, 351, 352, 353, 357, 360, 364, 366, 367, 382, 384, 386, 387, 391, 393, 395, 397, 399, 408, 411, 413, 415, 416, 419, 421], "summary": {"covered_lines": 27, "num_statements": 43, "percent_covered": 62.7906976744186, "percent_covered_display": "63", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [370, 371, 374, 378, 425, 428, 430, 434, 435, 439, 442, 443, 444, 445, 447, 458], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 18, 19, 25, 33, 34, 38, 39, 48, 49, 53, 54, 58, 59, 64, 65, 83, 104, 111, 130, 148, 166, 175, 180, 201, 202, 220, 237, 245, 253, 265, 286, 295, 300, 314, 315, 333, 355, 362, 380, 389, 401, 423, 432, 437], "summary": {"covered_lines": 47, "num_statements": 47, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/client.py": {"executed_lines": [1, 9, 11, 12, 14, 22, 25, 26, 55, 86, 89, 90, 91, 92, 93, 96, 101, 104, 105, 106, 108, 124, 127, 143, 144, 145, 147, 149, 150, 152, 154, 155, 157, 159, 160, 162, 164, 165, 169, 170, 174, 186, 188, 202, 229, 230, 233, 236, 249, 250, 251, 256, 257, 259, 279, 306, 307, 308, 309, 310, 311, 313, 316, 320, 347, 381, 406, 412, 414, 420, 421, 423, 429, 430, 432, 434, 435, 437, 439, 441, 443, 445, 469, 491, 503, 528, 555, 569], "summary": {"covered_lines": 86, "num_statements": 144, "percent_covered": 59.72222222222222, "percent_covered_display": "60", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [15, 16, 17, 18, 19, 20, 128, 131, 132, 133, 136, 137, 139, 167, 172, 200, 253, 277, 312, 314, 340, 369, 370, 371, 373, 399, 463, 464, 466, 467, 478, 479, 482, 483, 484, 487, 497, 498, 499, 501, 517, 518, 519, 521, 522, 523, 524, 526, 544, 545, 546, 553, 564, 565, 567, 578, 579, 581], "excluded_lines": [], "functions": {"ZenooClient.__init__": {"executed_lines": [86, 89, 90, 91, 92, 93, 96, 101, 104, 105, 106], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient._parse_host_or_url": {"executed_lines": [124, 127, 143, 144, 145, 147], "summary": {"covered_lines": 6, "num_statements": 13, "percent_covered": 46.15384615384615, "percent_covered_display": "46", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [128, 131, 132, 133, 136, 137, 139], "excluded_lines": []}, "ZenooClient.is_authenticated": {"executed_lines": [152], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.database": {"executed_lines": [157], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.uid": {"executed_lines": [162], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.username": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [167], "excluded_lines": []}, "ZenooClient.server_version": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [172], "excluded_lines": []}, "ZenooClient.login": {"executed_lines": [186], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.login_with_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [200], "excluded_lines": []}, "ZenooClient.execute_kw": {"executed_lines": [229, 230, 233, 236, 249, 250, 251, 256, 257], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [253], "excluded_lines": []}, "ZenooClient.execute": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [277], "excluded_lines": []}, "ZenooClient.search_read": {"executed_lines": [306, 307, 308, 309, 310, 311, 313, 316], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [312, 314], "excluded_lines": []}, "ZenooClient.search_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [340], "excluded_lines": []}, "ZenooClient.read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [369, 370, 371, 373], "excluded_lines": []}, "ZenooClient.get_model_fields": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [399], "excluded_lines": []}, "ZenooClient.health_check": {"executed_lines": [412], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.get_server_version": {"executed_lines": [420, 421], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.list_databases": {"executed_lines": [429, 430], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.close": {"executed_lines": [434, 435], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.__aenter__": {"executed_lines": [439], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.__aexit__": {"executed_lines": [443], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ZenooClient.model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [463, 464, 466, 467], "excluded_lines": []}, "ZenooClient.get_or_create_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [478, 479, 482, 483, 484, 487], "excluded_lines": []}, "ZenooClient.setup_transaction_manager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [497, 498, 499, 501], "excluded_lines": []}, "ZenooClient.setup_cache_manager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [517, 518, 519, 521, 522, 523, 524, 526], "excluded_lines": []}, "ZenooClient.setup_batch_manager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [544, 545, 546, 553], "excluded_lines": []}, "ZenooClient.transaction": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [564, 565, 567], "excluded_lines": []}, "ZenooClient.batch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [578, 579, 581], "excluded_lines": []}, "": {"executed_lines": [1, 9, 11, 12, 14, 22, 25, 26, 55, 108, 149, 150, 154, 155, 159, 160, 164, 165, 169, 170, 174, 188, 202, 259, 279, 320, 347, 381, 406, 414, 423, 432, 437, 441, 445, 469, 491, 503, 528, 555, 569], "summary": {"covered_lines": 39, "num_statements": 45, "percent_covered": 86.66666666666667, "percent_covered_display": "87", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [15, 16, 17, 18, 19, 20], "excluded_lines": []}}, "classes": {"ZenooClient": {"executed_lines": [86, 89, 90, 91, 92, 93, 96, 101, 104, 105, 106, 124, 127, 143, 144, 145, 147, 152, 157, 162, 186, 229, 230, 233, 236, 249, 250, 251, 256, 257, 306, 307, 308, 309, 310, 311, 313, 316, 412, 420, 421, 429, 430, 434, 435, 439, 443], "summary": {"covered_lines": 47, "num_statements": 99, "percent_covered": 47.474747474747474, "percent_covered_display": "47", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [128, 131, 132, 133, 136, 137, 139, 167, 172, 200, 253, 277, 312, 314, 340, 369, 370, 371, 373, 399, 463, 464, 466, 467, 478, 479, 482, 483, 484, 487, 497, 498, 499, 501, 517, 518, 519, 521, 522, 523, 524, 526, 544, 545, 546, 553, 564, 565, 567, 578, 579, 581], "excluded_lines": []}, "": {"executed_lines": [1, 9, 11, 12, 14, 22, 25, 26, 55, 108, 149, 150, 154, 155, 159, 160, 164, 165, 169, 170, 174, 188, 202, 259, 279, 320, 347, 381, 406, 414, 423, 432, 437, 441, 445, 469, 491, 503, 528, 555, 569], "summary": {"covered_lines": 39, "num_statements": 45, "percent_covered": 86.66666666666667, "percent_covered_display": "87", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [15, 16, 17, 18, 19, 20], "excluded_lines": []}}}, "src/zenoo_rpc/exceptions/__init__.py": {"executed_lines": [1, 8, 19, 22, 24], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 19, 22, 24], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 19, 22, 24], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/exceptions/base.py": {"executed_lines": [1, 8, 11, 12, 18, 19, 20, 21, 24, 25, 30, 33, 34, 38, 42, 45, 46, 51, 54, 55, 61, 67, 68, 71, 72, 78, 84, 85, 88, 89, 94, 97, 98, 104, 110, 111], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"ZenooError.__init__": {"executed_lines": [19, 20, 21], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationError.__init__": {"executed_lines": [67, 68], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AccessError.__init__": {"executed_lines": [84, 85], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InternalError.__init__": {"executed_lines": [110, 111], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 11, 12, 18, 24, 25, 30, 33, 34, 38, 42, 45, 46, 51, 54, 55, 61, 71, 72, 78, 88, 89, 94, 97, 98, 104], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ZenooError": {"executed_lines": [19, 20, 21], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConnectionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequestTimeoutError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthenticationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationError": {"executed_lines": [67, 68], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AccessError": {"executed_lines": [84, 85], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MethodNotFoundError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InternalError": {"executed_lines": [110, 111], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 11, 12, 18, 24, 25, 30, 33, 34, 38, 42, 45, 46, 51, 54, 55, 61, 71, 72, 78, 88, 89, 94, 97, 98, 104], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/exceptions/mapping.py": {"executed_lines": [1, 8, 10, 20, 39, 40, 41, 44, 50, 51, 55, 56, 60, 61, 65, 66, 70, 71, 78, 79, 81, 82, 87, 88, 92, 93, 97, 98, 104, 110], "summary": {"covered_lines": 29, "num_statements": 30, "percent_covered": 96.66666666666667, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [119], "excluded_lines": [], "functions": {"map_jsonrpc_error": {"executed_lines": [39, 40, 41, 44, 50, 51, 55, 56, 60, 61, 65, 66, 70, 71, 78, 79, 81, 82, 87, 88, 92, 93, 97, 98, 104], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "extract_server_traceback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [119], "excluded_lines": []}, "": {"executed_lines": [1, 8, 10, 20, 110], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 10, 20, 39, 40, 41, 44, 50, 51, 55, 56, 60, 61, 65, 66, 70, 71, 78, 79, 81, 82, 87, 88, 92, 93, 97, 98, 104, 110], "summary": {"covered_lines": 29, "num_statements": 30, "percent_covered": 96.66666666666667, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [119], "excluded_lines": []}}}, "src/zenoo_rpc/models/__init__.py": {"executed_lines": [1, 8, 9, 18, 19, 21], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 18, 19, 21], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 18, 19, 21], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/models/base.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 19, 22, 23, 25, 27, 30, 31, 34, 36, 38, 39, 41, 44, 46, 51, 52, 54, 55, 56, 57, 60, 62, 63, 64, 65, 68, 70, 71, 72, 73, 76, 79, 80, 105, 119, 122, 123, 124, 125, 129, 132, 134, 137, 138, 139, 142, 145, 148, 149, 152, 154, 155, 161, 163, 164, 173, 175, 176, 182, 183, 185, 187, 188, 190, 194, 196, 203, 206, 208, 210, 213, 214, 216, 225, 227, 233, 235, 244, 247, 262, 264, 277, 289, 291, 292, 293, 294, 296, 298, 299, 303, 304, 305], "summary": {"covered_lines": 91, "num_statements": 108, "percent_covered": 84.25925925925925, "percent_covered_display": "84", "missing_lines": 17, "excluded_lines": 6}, "missing_lines": [42, 49, 189, 192, 248, 249, 252, 254, 258, 260, 270, 271, 275, 282, 283, 287, 300], "excluded_lines": [289, 290, 291, 292, 293, 294], "functions": {"OdooModelMeta.__new__": {"executed_lines": [27, 30, 31, 34, 36], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModelMeta._setup_relationship_descriptors": {"executed_lines": [41, 44, 46, 51, 52, 54, 55, 56, 57, 60, 62, 63, 64, 65, 68, 70, 71, 72, 73, 76], "summary": {"covered_lines": 20, "num_statements": 22, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [42, 49], "excluded_lines": []}, "OdooModel.__init__": {"executed_lines": [132, 134, 137, 138, 139, 142, 145, 148, 149, 152], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModel.get_odoo_name": {"executed_lines": [161], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModel.get_field_info": {"executed_lines": [173], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModel.get_relationship_fields": {"executed_lines": [182, 183, 185, 187, 188, 190, 194], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [189, 192], "excluded_lines": []}, "OdooModel.__getattribute__": {"executed_lines": [203, 206], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModel.__setattr__": {"executed_lines": [210, 213, 214], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModel.is_field_loaded": {"executed_lines": [225], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModel.get_loaded_fields": {"executed_lines": [233], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OdooModel.to_odoo_dict": {"executed_lines": [244, 247, 262], "summary": {"covered_lines": 3, "num_statements": 9, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [248, 249, 252, 254, 258, 260], "excluded_lines": []}, "OdooModel.refresh": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [270, 271, 275], "excluded_lines": []}, "OdooModel.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [282, 283, 287], "excluded_lines": []}, "OdooModel.__repr__": {"executed_lines": [291, 292, 293, 294], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 5}, "missing_lines": [], "excluded_lines": [290, 291, 292, 293, 294]}, "OdooModel.__str__": {"executed_lines": [298, 299], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [300], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 19, 22, 23, 25, 38, 39, 79, 80, 105, 119, 122, 123, 124, 125, 129, 154, 155, 163, 164, 175, 176, 196, 208, 216, 227, 235, 264, 277, 289, 296, 303, 304, 305], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [289]}}, "classes": {"OdooModelMeta": {"executed_lines": [27, 30, 31, 34, 36, 41, 44, 46, 51, 52, 54, 55, 56, 57, 60, 62, 63, 64, 65, 68, 70, 71, 72, 73, 76], "summary": {"covered_lines": 25, "num_statements": 27, "percent_covered": 92.5925925925926, "percent_covered_display": "93", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [42, 49], "excluded_lines": []}, "OdooModel": {"executed_lines": [132, 134, 137, 138, 139, 142, 145, 148, 149, 152, 161, 173, 182, 183, 185, 187, 188, 190, 194, 203, 206, 210, 213, 214, 225, 233, 244, 247, 262, 291, 292, 293, 294, 298, 299], "summary": {"covered_lines": 31, "num_statements": 46, "percent_covered": 67.3913043478261, "percent_covered_display": "67", "missing_lines": 15, "excluded_lines": 5}, "missing_lines": [189, 192, 248, 249, 252, 254, 258, 260, 270, 271, 275, 282, 283, 287, 300], "excluded_lines": [290, 291, 292, 293, 294]}, "OdooRecord": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 19, 22, 23, 25, 38, 39, 79, 80, 105, 119, 122, 123, 124, 125, 129, 154, 155, 163, 164, 175, 176, 196, 208, 216, 227, 235, 264, 277, 289, 296, 303, 304, 305], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [289]}}}, "src/zenoo_rpc/models/common.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 18, 21, 22, 23, 24, 44, 45, 48, 49, 50, 51, 54, 55, 56, 57, 58, 61, 66, 67, 70, 71, 74, 77, 82, 83, 84, 87, 88, 90, 91, 93, 95, 96, 98, 100, 101, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 115, 116, 117, 119, 120, 121, 122, 127, 128, 129, 131, 132, 133, 138, 139, 140, 142, 143, 144, 145, 148, 149, 150, 152, 153, 154, 155, 158, 163, 168, 169, 170, 172, 173, 176, 181, 182, 183, 185, 186, 189, 190, 191, 193, 194, 195, 198, 205, 206, 209, 214, 215, 216, 219, 220, 221, 223, 224, 227, 232, 233, 234, 236, 237, 242, 243, 246, 255, 256, 257, 260, 265, 266, 267, 269, 272, 276, 277, 278, 279], "summary": {"covered_lines": 121, "num_statements": 121, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"ResPartner.is_customer": {"executed_lines": [93], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResPartner.is_vendor": {"executed_lines": [98], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResPartner.full_address": {"executed_lines": [103, 104, 105, 106, 107, 108, 109, 110, 111, 112], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 18, 21, 22, 23, 24, 44, 45, 48, 49, 50, 51, 54, 55, 56, 57, 58, 61, 66, 67, 70, 71, 74, 77, 82, 83, 84, 87, 88, 90, 91, 95, 96, 100, 101, 115, 116, 117, 119, 120, 121, 122, 127, 128, 129, 131, 132, 133, 138, 139, 140, 142, 143, 144, 145, 148, 149, 150, 152, 153, 154, 155, 158, 163, 168, 169, 170, 172, 173, 176, 181, 182, 183, 185, 186, 189, 190, 191, 193, 194, 195, 198, 205, 206, 209, 214, 215, 216, 219, 220, 221, 223, 224, 227, 232, 233, 234, 236, 237, 242, 243, 246, 255, 256, 257, 260, 265, 266, 267, 269, 272, 276, 277, 278, 279], "summary": {"covered_lines": 109, "num_statements": 109, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ResPartner": {"executed_lines": [93, 98, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResCountry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResCountryState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResCurrency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResUsers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResGroups": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResGroupsCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProductProduct": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProductCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SaleOrder": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SaleOrderLine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 18, 21, 22, 23, 24, 44, 45, 48, 49, 50, 51, 54, 55, 56, 57, 58, 61, 66, 67, 70, 71, 74, 77, 82, 83, 84, 87, 88, 90, 91, 95, 96, 100, 101, 115, 116, 117, 119, 120, 121, 122, 127, 128, 129, 131, 132, 133, 138, 139, 140, 142, 143, 144, 145, 148, 149, 150, 152, 153, 154, 155, 158, 163, 168, 169, 170, 172, 173, 176, 181, 182, 183, 185, 186, 189, 190, 191, 193, 194, 195, 198, 205, 206, 209, 214, 215, 216, 219, 220, 221, 223, 224, 227, 232, 233, 234, 236, 237, 242, 243, 246, 255, 256, 257, 260, 265, 266, 267, 269, 272, 276, 277, 278, 279], "summary": {"covered_lines": 109, "num_statements": 109, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/models/fields.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 19, 22, 23, 25, 26, 27, 28, 29, 31, 32, 36, 37, 38, 43, 44, 48, 52, 53, 54, 64, 65, 69, 78, 79, 81, 82, 83, 86, 87, 89, 90, 91, 94, 95, 97, 98, 99, 102, 123, 124, 135, 159, 171, 194, 199, 202, 209, 231, 241, 269, 287, 297, 315, 325, 345, 356, 377, 381, 384, 391, 409, 419, 437, 447, 468, 472, 473, 475, 482, 500], "summary": {"covered_lines": 73, "num_statements": 86, "percent_covered": 84.88372093023256, "percent_covered_display": "85", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [16, 17, 33, 40, 45, 49, 67, 71, 74, 75, 200, 259, 382], "excluded_lines": [], "functions": {"RelationshipDescriptor.__init__": {"executed_lines": [26, 27, 28, 29], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RelationshipDescriptor.__get__": {"executed_lines": [32, 36, 37, 38, 43, 44, 48, 52, 53, 54, 64, 65], "summary": {"covered_lines": 12, "num_statements": 17, "percent_covered": 70.58823529411765, "percent_covered_display": "71", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [33, 40, 45, 49, 67], "excluded_lines": []}, "RelationshipDescriptor.__set__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [71, 74, 75], "excluded_lines": []}, "Many2OneDescriptor.__init__": {"executed_lines": [82, 83], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "One2ManyDescriptor.__init__": {"executed_lines": [90, 91], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Many2ManyDescriptor.__init__": {"executed_lines": [98, 99], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Many2OneField": {"executed_lines": [123, 124], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "One2ManyField": {"executed_lines": [159], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Many2ManyField": {"executed_lines": [194, 199, 202], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [200], "excluded_lines": []}, "SelectionField": {"executed_lines": [231], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BinaryField": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [259], "excluded_lines": []}, "DateField": {"executed_lines": [287], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DateTimeField": {"executed_lines": [315], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MonetaryField": {"executed_lines": [345], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FloatField": {"executed_lines": [377, 381, 384], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [382], "excluded_lines": []}, "IntegerField": {"executed_lines": [409], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TextField": {"executed_lines": [437], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CharField": {"executed_lines": [468, 472, 473, 475], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BooleanField": {"executed_lines": [500], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 19, 22, 23, 25, 31, 69, 78, 79, 81, 86, 87, 89, 94, 95, 97, 102, 135, 171, 209, 241, 269, 297, 325, 356, 391, 419, 447, 482], "summary": {"covered_lines": 31, "num_statements": 33, "percent_covered": 93.93939393939394, "percent_covered_display": "94", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [16, 17], "excluded_lines": []}}, "classes": {"RelationshipDescriptor": {"executed_lines": [26, 27, 28, 29, 32, 36, 37, 38, 43, 44, 48, 52, 53, 54, 64, 65], "summary": {"covered_lines": 16, "num_statements": 24, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [33, 40, 45, 49, 67, 71, 74, 75], "excluded_lines": []}, "Many2OneDescriptor": {"executed_lines": [82, 83], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "One2ManyDescriptor": {"executed_lines": [90, 91], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Many2ManyDescriptor": {"executed_lines": [98, 99], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 19, 22, 23, 25, 31, 69, 78, 79, 81, 86, 87, 89, 94, 95, 97, 102, 123, 124, 135, 159, 171, 194, 199, 202, 209, 231, 241, 269, 287, 297, 315, 325, 345, 356, 377, 381, 384, 391, 409, 419, 437, 447, 468, 472, 473, 475, 482, 500], "summary": {"covered_lines": 51, "num_statements": 56, "percent_covered": 91.07142857142857, "percent_covered_display": "91", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [16, 17, 200, 259, 382], "excluded_lines": []}}}, "src/zenoo_rpc/models/registry.py": {"executed_lines": [1, 9, 10, 11, 12, 13, 15, 16, 18, 19, 25, 27, 30, 31, 58, 60, 61, 62, 65, 81, 95, 97, 100, 102, 103, 105, 107, 114, 115, 116, 118, 127, 129, 138, 140, 146, 148, 201, 237, 277, 287, 288, 291, 293, 294, 295, 297, 302, 303, 307, 319, 325, 336, 338, 339, 340, 342, 347, 352, 361, 376, 378, 379, 383, 385, 397, 409, 416, 419, 428, 431, 443], "summary": {"covered_lines": 70, "num_statements": 145, "percent_covered": 48.275862068965516, "percent_covered_display": "48", "missing_lines": 75, "excluded_lines": 0}, "missing_lines": [165, 166, 169, 172, 173, 180, 183, 186, 193, 196, 198, 199, 216, 217, 219, 221, 229, 231, 233, 234, 235, 249, 251, 253, 254, 257, 260, 261, 262, 265, 266, 267, 269, 271, 273, 275, 305, 309, 310, 312, 314, 315, 317, 321, 322, 323, 327, 328, 330, 334, 344, 345, 349, 350, 354, 355, 359, 363, 364, 366, 371, 372, 374, 387, 388, 390, 395, 399, 400, 402, 407, 411, 412, 440, 449], "excluded_lines": [], "functions": {"ModelRegistry.__init__": {"executed_lines": [60, 61, 62, 65], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry.register": {"executed_lines": [95, 105], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry.register.decorator": {"executed_lines": [97, 100, 102, 103], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry.register_model": {"executed_lines": [114, 115, 116], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry.get_model": {"executed_lines": [127], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry.has_model": {"executed_lines": [138], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry.list_models": {"executed_lines": [146], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry.create_dynamic_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [165, 166, 169, 172, 173, 180, 183, 186, 193, 196, 198, 199], "excluded_lines": []}, "ModelRegistry._get_field_definitions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [216, 217, 219, 221, 229, 231, 233, 234, 235], "excluded_lines": []}, "ModelRegistry._create_pydantic_fields": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [249, 251, 253, 254, 257, 260, 261, 262, 265, 266, 267, 269, 271, 273, 275], "excluded_lines": []}, "ModelRegistry._generate_class_name": {"executed_lines": [287, 288], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry._create_char_field": {"executed_lines": [293, 294, 295, 297, 302, 303], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [305], "excluded_lines": []}, "ModelRegistry._create_text_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [309, 310, 312, 314, 315, 317], "excluded_lines": []}, "ModelRegistry._create_integer_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [321, 322, 323], "excluded_lines": []}, "ModelRegistry._create_float_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [327, 328, 330, 334], "excluded_lines": []}, "ModelRegistry._create_boolean_field": {"executed_lines": [338, 339, 340], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry._create_date_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [344, 345], "excluded_lines": []}, "ModelRegistry._create_datetime_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [349, 350], "excluded_lines": []}, "ModelRegistry._create_monetary_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [354, 355, 359], "excluded_lines": []}, "ModelRegistry._create_selection_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [363, 364, 366, 371, 372, 374], "excluded_lines": []}, "ModelRegistry._create_many2one_field": {"executed_lines": [378, 379, 383], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelRegistry._create_one2many_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [387, 388, 390, 395], "excluded_lines": []}, "ModelRegistry._create_many2many_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [399, 400, 402, 407], "excluded_lines": []}, "ModelRegistry._create_binary_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [411, 412], "excluded_lines": []}, "register_model": {"executed_lines": [428], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_model_class": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [440], "excluded_lines": []}, "get_registry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [449], "excluded_lines": []}, "": {"executed_lines": [1, 9, 10, 11, 12, 13, 15, 16, 18, 19, 25, 27, 30, 31, 58, 81, 107, 118, 129, 140, 148, 201, 237, 277, 291, 307, 319, 325, 336, 342, 347, 352, 361, 376, 385, 397, 409, 416, 419, 431, 443], "summary": {"covered_lines": 39, "num_statements": 39, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ModelRegistry": {"executed_lines": [60, 61, 62, 65, 95, 97, 100, 102, 103, 105, 114, 115, 116, 127, 138, 146, 287, 288, 293, 294, 295, 297, 302, 303, 338, 339, 340, 378, 379, 383], "summary": {"covered_lines": 30, "num_statements": 103, "percent_covered": 29.12621359223301, "percent_covered_display": "29", "missing_lines": 73, "excluded_lines": 0}, "missing_lines": [165, 166, 169, 172, 173, 180, 183, 186, 193, 196, 198, 199, 216, 217, 219, 221, 229, 231, 233, 234, 235, 249, 251, 253, 254, 257, 260, 261, 262, 265, 266, 267, 269, 271, 273, 275, 305, 309, 310, 312, 314, 315, 317, 321, 322, 323, 327, 328, 330, 334, 344, 345, 349, 350, 354, 355, 359, 363, 364, 366, 371, 372, 374, 387, 388, 390, 395, 399, 400, 402, 407, 411, 412], "excluded_lines": []}, "": {"executed_lines": [1, 9, 10, 11, 12, 13, 15, 16, 18, 19, 25, 27, 30, 31, 58, 81, 107, 118, 129, 140, 148, 201, 237, 277, 291, 307, 319, 325, 336, 342, 347, 352, 361, 376, 385, 397, 409, 416, 419, 428, 431, 443], "summary": {"covered_lines": 40, "num_statements": 42, "percent_covered": 95.23809523809524, "percent_covered_display": "95", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [440, 449], "excluded_lines": []}}}, "src/zenoo_rpc/models/relationships.py": {"executed_lines": [1, 8, 9, 10, 12, 15, 16, 36, 55, 56, 57, 58, 59, 60, 63, 64, 65, 68, 69, 72, 73, 74, 76, 83, 84, 87, 88, 89, 90, 91, 94, 98, 100, 102, 105, 106, 108, 111, 118, 122, 123, 125, 128, 130, 131, 135, 136, 137, 138, 141, 143, 150, 152, 159, 162, 163, 164, 176, 179, 183, 186, 187, 198, 200, 248, 256, 264, 272, 274, 276, 282, 283, 289, 296, 297, 298, 300, 335, 366, 377, 441, 446], "summary": {"covered_lines": 78, "num_statements": 175, "percent_covered": 44.57142857142857, "percent_covered_display": "45", "missing_lines": 97, "excluded_lines": 4}, "missing_lines": [95, 112, 113, 114, 115, 132, 139, 145, 146, 147, 148, 166, 167, 173, 177, 181, 189, 191, 192, 193, 194, 202, 203, 204, 205, 206, 210, 212, 214, 215, 223, 225, 228, 229, 236, 238, 240, 241, 243, 245, 246, 254, 262, 266, 267, 268, 269, 270, 319, 322, 332, 333, 349, 350, 352, 354, 355, 357, 360, 361, 362, 363, 364, 375, 389, 391, 392, 393, 394, 395, 396, 397, 400, 402, 403, 404, 405, 406, 408, 410, 412, 413, 420, 423, 424, 425, 426, 432, 434, 435, 437, 439, 443, 444, 452, 453, 454], "excluded_lines": [276, 277, 278, 279], "functions": {"LazyRelationship.__init__": {"executed_lines": [55, 56, 57, 58, 59, 60, 63, 64, 65, 68, 69, 72, 73, 74], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LazyRelationship.load": {"executed_lines": [83, 84, 87, 88, 89, 90, 91, 94, 98], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [95], "excluded_lines": []}, "LazyRelationship._load_with_batching": {"executed_lines": [102, 105, 106, 108, 111, 118, 122, 123], "summary": {"covered_lines": 8, "num_statements": 12, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [112, 113, 114, 115], "excluded_lines": []}, "LazyRelationship._execute_batch_load": {"executed_lines": [128, 130, 131, 135, 136, 137, 138, 141, 143, 150, 152, 159, 162, 163, 164, 176, 179, 183, 186, 187, 198], "summary": {"covered_lines": 21, "num_statements": 37, "percent_covered": 56.75675675675676, "percent_covered_display": "57", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [132, 139, 145, 146, 147, 148, 166, 167, 173, 177, 181, 189, 191, 192, 193, 194], "excluded_lines": []}, "LazyRelationship._fetch_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [202, 203, 204, 205, 206, 210, 212, 214, 215, 223, 225, 228, 229, 236, 238, 240, 241, 243, 245, 246], "excluded_lines": []}, "LazyRelationship.is_loaded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [254], "excluded_lines": []}, "LazyRelationship.get_cached_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [262], "excluded_lines": []}, "LazyRelationship.invalidate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [266, 267, 268, 269, 270], "excluded_lines": []}, "LazyRelationship.__await__": {"executed_lines": [274], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LazyRelationship.__repr__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 3}, "missing_lines": [], "excluded_lines": [277, 278, 279]}, "RelationshipManager.__init__": {"executed_lines": [296, 297, 298], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RelationshipManager.create_relationship": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [319, 322, 332, 333], "excluded_lines": []}, "RelationshipManager._parse_relation_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [349, 350, 352, 354, 355, 357, 360, 361, 362, 363, 364], "excluded_lines": []}, "RelationshipManager.get_relationship": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [375], "excluded_lines": []}, "RelationshipManager.prefetch_relationships": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [389, 391, 392, 393, 394, 395, 396, 397, 400, 402, 403, 404, 405, 406, 408, 410, 412, 413, 420, 423, 424, 425, 426, 432, 434, 435, 437, 439], "excluded_lines": []}, "RelationshipManager.invalidate_all": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [443, 444], "excluded_lines": []}, "RelationshipManager.invalidate_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [452, 453, 454], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 15, 16, 36, 76, 100, 125, 200, 248, 256, 264, 272, 276, 282, 283, 289, 300, 335, 366, 377, 441, 446], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [276]}}, "classes": {"LazyRelationship": {"executed_lines": [55, 56, 57, 58, 59, 60, 63, 64, 65, 68, 69, 72, 73, 74, 83, 84, 87, 88, 89, 90, 91, 94, 98, 102, 105, 106, 108, 111, 118, 122, 123, 128, 130, 131, 135, 136, 137, 138, 141, 143, 150, 152, 159, 162, 163, 164, 176, 179, 183, 186, 187, 198, 274], "summary": {"covered_lines": 53, "num_statements": 101, "percent_covered": 52.475247524752476, "percent_covered_display": "52", "missing_lines": 48, "excluded_lines": 3}, "missing_lines": [95, 112, 113, 114, 115, 132, 139, 145, 146, 147, 148, 166, 167, 173, 177, 181, 189, 191, 192, 193, 194, 202, 203, 204, 205, 206, 210, 212, 214, 215, 223, 225, 228, 229, 236, 238, 240, 241, 243, 245, 246, 254, 262, 266, 267, 268, 269, 270], "excluded_lines": [277, 278, 279]}, "RelationshipManager": {"executed_lines": [296, 297, 298], "summary": {"covered_lines": 3, "num_statements": 52, "percent_covered": 5.769230769230769, "percent_covered_display": "6", "missing_lines": 49, "excluded_lines": 0}, "missing_lines": [319, 322, 332, 333, 349, 350, 352, 354, 355, 357, 360, 361, 362, 363, 364, 375, 389, 391, 392, 393, 394, 395, 396, 397, 400, 402, 403, 404, 405, 406, 408, 410, 412, 413, 420, 423, 424, 425, 426, 432, 434, 435, 437, 439, 443, 444, 452, 453, 454], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 15, 16, 36, 76, 100, 125, 200, 248, 256, 264, 272, 276, 282, 283, 289, 300, 335, 366, 377, 441, 446], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [276]}}}, "src/zenoo_rpc/query/__init__.py": {"executed_lines": [1, 8, 9, 10, 11, 17], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 11, 17], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 17], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/query/builder.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 23, 24, 53, 76, 77, 78, 79, 80, 81, 82, 83, 86, 87, 88, 91, 92, 95, 98, 99, 101, 121, 124, 125, 126, 127, 130, 131, 132, 133, 135, 137, 148, 149, 150, 151, 155, 174, 177, 178, 179, 180, 182, 184, 185, 187, 196, 198, 207, 209, 222, 224, 233, 235, 236, 241, 243, 252, 253, 254, 256, 274, 275, 278, 280, 281, 283, 300, 301, 304, 306, 307, 309, 315, 316, 319, 322, 323, 324, 325, 328, 332, 333, 335, 337, 344, 345, 346, 348, 360, 361, 363, 365, 368, 370, 376, 380, 387, 388, 390, 399, 415, 436, 440, 446, 455, 467, 468, 471, 472, 474, 476, 495, 502, 503, 504, 507, 509, 511, 512, 513, 515, 519, 520, 523, 530, 532, 534, 544, 547, 549, 558, 560, 571, 572, 574, 576, 586, 587, 588, 589, 591, 593, 594, 596, 597, 599, 601, 602, 604, 605, 607, 617, 618, 628, 635, 636, 639, 641, 647, 649, 659, 661, 673, 684, 693, 695, 705, 712, 715, 717, 739, 749, 751, 753, 754, 757, 758], "summary": {"covered_lines": 185, "num_statements": 228, "percent_covered": 81.14035087719299, "percent_covered_display": "81", "missing_lines": 43, "excluded_lines": 8}, "missing_lines": [153, 175, 239, 276, 302, 329, 364, 366, 377, 396, 397, 408, 409, 411, 413, 425, 426, 428, 430, 431, 432, 434, 438, 442, 443, 444, 482, 483, 486, 488, 489, 510, 514, 516, 609, 610, 613, 614, 671, 682, 727, 734, 737], "excluded_lines": [549, 550, 551, 552, 553, 554, 555, 556], "functions": {"QuerySet.__init__": {"executed_lines": [76, 77, 78, 79, 80, 81, 82, 83, 86, 87, 88, 91, 92, 95, 98, 99], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.filter": {"executed_lines": [121, 124, 125, 126, 127, 130, 131, 132, 133, 135], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.exclude": {"executed_lines": [148, 149, 150, 151], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [153], "excluded_lines": []}, "QuerySet.order_by": {"executed_lines": [174, 177, 178, 179, 180, 182, 184, 185], "summary": {"covered_lines": 8, "num_statements": 9, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [175], "excluded_lines": []}, "QuerySet.limit": {"executed_lines": [196], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.offset": {"executed_lines": [207], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.only": {"executed_lines": [222], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.defer": {"executed_lines": [233, 235, 236, 241], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [239], "excluded_lines": []}, "QuerySet.with_context": {"executed_lines": [252, 253, 254], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.select_related": {"executed_lines": [274, 275, 278, 280, 281], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [276], "excluded_lines": []}, "QuerySet.prefetch_related": {"executed_lines": [300, 301, 304, 306, 307], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [302], "excluded_lines": []}, "QuerySet.all": {"executed_lines": [315, 316, 319, 322, 323, 324, 325, 328, 332, 333, 335], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [329], "excluded_lines": []}, "QuerySet.first": {"executed_lines": [344, 345, 346], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.get": {"executed_lines": [360, 361, 363, 365, 368], "summary": {"covered_lines": 5, "num_statements": 7, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [364, 366], "excluded_lines": []}, "QuerySet.count": {"executed_lines": [376, 380, 387, 388], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [377], "excluded_lines": []}, "QuerySet.exists": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [396, 397], "excluded_lines": []}, "QuerySet.values": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [408, 409, 411, 413], "excluded_lines": []}, "QuerySet.values_list": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [425, 426, 428, 430, 431, 432, 434], "excluded_lines": []}, "QuerySet.__aiter__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [438], "excluded_lines": []}, "QuerySet._async_iterator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [442, 443, 444], "excluded_lines": []}, "QuerySet._clone": {"executed_lines": [455, 467, 468, 471, 472, 474], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet._handle_prefetch_related": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [482, 483, 486, 488, 489], "excluded_lines": []}, "QuerySet._execute_query": {"executed_lines": [502, 503, 504, 507, 509, 511, 512, 513, 515, 519, 520, 523, 530, 532], "summary": {"covered_lines": 14, "num_statements": 17, "percent_covered": 82.3529411764706, "percent_covered_display": "82", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [510, 514, 516], "excluded_lines": []}, "QuerySet._create_model_instance": {"executed_lines": [544, 547], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.__repr__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 7}, "missing_lines": [], "excluded_lines": [550, 551, 552, 553, 554, 555, 556]}, "QuerySet._generate_cache_key": {"executed_lines": [560, 571, 572, 574], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet.cache": {"executed_lines": [586, 587, 588, 589], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet._get_cached_result": {"executed_lines": [593, 594, 596, 597], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet._set_cached_result": {"executed_lines": [601, 602, 604, 605], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QuerySet._invalidate_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [609, 610, 613, 614], "excluded_lines": []}, "QueryBuilder.__init__": {"executed_lines": [635, 636, 639], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryBuilder.all": {"executed_lines": [647], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryBuilder.filter": {"executed_lines": [659], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryBuilder.exclude": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [671], "excluded_lines": []}, "QueryBuilder.order_by": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [682], "excluded_lines": []}, "QueryBuilder.get": {"executed_lines": [693], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryBuilder.create": {"executed_lines": [705, 712, 715], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryBuilder.bulk_create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [727, 734, 737], "excluded_lines": []}, "QueryBuilder.__call__": {"executed_lines": [749], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryBuilder._invalidate_cache": {"executed_lines": [753, 754, 757, 758], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 23, 24, 53, 101, 137, 155, 187, 198, 209, 224, 243, 256, 283, 309, 337, 348, 370, 390, 399, 415, 436, 440, 446, 476, 495, 534, 549, 558, 576, 591, 599, 607, 617, 618, 628, 641, 649, 661, 673, 684, 695, 717, 739, 751], "summary": {"covered_lines": 52, "num_statements": 52, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [549]}}, "classes": {"QuerySet": {"executed_lines": [76, 77, 78, 79, 80, 81, 82, 83, 86, 87, 88, 91, 92, 95, 98, 99, 121, 124, 125, 126, 127, 130, 131, 132, 133, 135, 148, 149, 150, 151, 174, 177, 178, 179, 180, 182, 184, 185, 196, 207, 222, 233, 235, 236, 241, 252, 253, 254, 274, 275, 278, 280, 281, 300, 301, 304, 306, 307, 315, 316, 319, 322, 323, 324, 325, 328, 332, 333, 335, 344, 345, 346, 360, 361, 363, 365, 368, 376, 380, 387, 388, 455, 467, 468, 471, 472, 474, 502, 503, 504, 507, 509, 511, 512, 513, 515, 519, 520, 523, 530, 532, 544, 547, 560, 571, 572, 574, 586, 587, 588, 589, 593, 594, 596, 597, 601, 602, 604, 605], "summary": {"covered_lines": 119, "num_statements": 157, "percent_covered": 75.79617834394904, "percent_covered_display": "76", "missing_lines": 38, "excluded_lines": 7}, "missing_lines": [153, 175, 239, 276, 302, 329, 364, 366, 377, 396, 397, 408, 409, 411, 413, 425, 426, 428, 430, 431, 432, 434, 438, 442, 443, 444, 482, 483, 486, 488, 489, 510, 514, 516, 609, 610, 613, 614], "excluded_lines": [550, 551, 552, 553, 554, 555, 556]}, "QueryBuilder": {"executed_lines": [635, 636, 639, 647, 659, 693, 705, 712, 715, 749, 753, 754, 757, 758], "summary": {"covered_lines": 14, "num_statements": 19, "percent_covered": 73.6842105263158, "percent_covered_display": "74", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [671, 682, 727, 734, 737], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 23, 24, 53, 101, 137, 155, 187, 198, 209, 224, 243, 256, 283, 309, 337, 348, 370, 390, 399, 415, 436, 440, 446, 476, 495, 534, 549, 558, 576, 591, 599, 607, 617, 618, 628, 641, 649, 661, 673, 684, 695, 717, 739, 751], "summary": {"covered_lines": 52, "num_statements": 52, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [549]}}}, "src/zenoo_rpc/query/expressions.py": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 24, 26, 28, 30, 32, 37, 38, 54, 60, 62, 64, 66, 70, 72, 74, 76, 78, 82, 84, 86, 88, 90, 92, 94, 96, 98, 102, 106, 110, 114, 118, 120, 124, 125, 127, 128, 129, 130, 132, 134, 137, 138, 140, 141, 144, 145, 147, 148, 151, 152, 154, 155, 158, 159, 161, 162, 165, 166, 168, 172, 173, 175, 176, 179, 180, 182, 183, 186, 187, 189, 190, 193, 194, 196, 197, 200, 201, 203, 207, 208, 210, 216, 217, 219, 225, 226, 228, 235, 236, 238, 239, 242, 243, 245, 246, 248, 250, 251, 252, 253, 254, 257, 258, 260, 261, 263, 265, 268, 269, 270, 271, 272, 275, 276, 278, 281], "summary": {"covered_lines": 102, "num_statements": 123, "percent_covered": 82.92682926829268, "percent_covered_display": "83", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [22, 34, 68, 80, 100, 104, 108, 112, 116, 169, 204, 212, 213, 221, 222, 230, 231, 266, 279, 283, 284], "excluded_lines": [], "functions": {"Expression.to_domain": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [22], "excluded_lines": []}, "Expression.__and__": {"executed_lines": [26], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Expression.__or__": {"executed_lines": [30], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Expression.__invert__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [34], "excluded_lines": []}, "Field.__init__": {"executed_lines": [60], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.__eq__": {"executed_lines": [64], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.__ne__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [68], "excluded_lines": []}, "Field.__gt__": {"executed_lines": [72], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.__ge__": {"executed_lines": [76], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.__lt__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [80], "excluded_lines": []}, "Field.__le__": {"executed_lines": [84], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.like": {"executed_lines": [88], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.ilike": {"executed_lines": [92], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.in_": {"executed_lines": [96], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Field.not_in": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [100], "excluded_lines": []}, "Field.contains": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [104], "excluded_lines": []}, "Field.startswith": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [108], "excluded_lines": []}, "Field.endswith": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [112], "excluded_lines": []}, "Field.is_null": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [116], "excluded_lines": []}, "Field.is_not_null": {"executed_lines": [120], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComparisonExpression.__init__": {"executed_lines": [128, 129, 130], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComparisonExpression.to_domain": {"executed_lines": [134], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Equal.__init__": {"executed_lines": [141], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NotEqual.__init__": {"executed_lines": [148], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GreaterThan.__init__": {"executed_lines": [155], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GreaterEqual.__init__": {"executed_lines": [162], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LessThan.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [169], "excluded_lines": []}, "LessEqual.__init__": {"executed_lines": [176], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Like.__init__": {"executed_lines": [183], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ILike.__init__": {"executed_lines": [190], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "In.__init__": {"executed_lines": [197], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NotIn.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [204], "excluded_lines": []}, "Contains.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [212, 213], "excluded_lines": []}, "StartsWith.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [221, 222], "excluded_lines": []}, "EndsWith.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [230, 231], "excluded_lines": []}, "LogicalExpression.__init__": {"executed_lines": [239], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AndExpression.__init__": {"executed_lines": [246], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AndExpression.to_domain": {"executed_lines": [250, 251, 252, 253, 254], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrExpression.__init__": {"executed_lines": [261], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrExpression.to_domain": {"executed_lines": [265, 268, 269, 270, 271, 272], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [266], "excluded_lines": []}, "NotExpression.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [279], "excluded_lines": []}, "NotExpression.to_domain": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [283, 284], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 24, 28, 32, 37, 38, 54, 62, 66, 70, 74, 78, 82, 86, 90, 94, 98, 102, 106, 110, 114, 118, 124, 125, 127, 132, 137, 138, 140, 144, 145, 147, 151, 152, 154, 158, 159, 161, 165, 166, 168, 172, 173, 175, 179, 180, 182, 186, 187, 189, 193, 194, 196, 200, 201, 203, 207, 208, 210, 216, 217, 219, 225, 226, 228, 235, 236, 238, 242, 243, 245, 248, 257, 258, 260, 263, 275, 276, 278, 281], "summary": {"covered_lines": 65, "num_statements": 65, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Expression": {"executed_lines": [26, 30], "summary": {"covered_lines": 2, "num_statements": 4, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [22, 34], "excluded_lines": []}, "Field": {"executed_lines": [60, 64, 72, 76, 84, 88, 92, 96, 120], "summary": {"covered_lines": 9, "num_statements": 16, "percent_covered": 56.25, "percent_covered_display": "56", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [68, 80, 100, 104, 108, 112, 116], "excluded_lines": []}, "ComparisonExpression": {"executed_lines": [128, 129, 130, 134], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Equal": {"executed_lines": [141], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NotEqual": {"executed_lines": [148], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GreaterThan": {"executed_lines": [155], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GreaterEqual": {"executed_lines": [162], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LessThan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [169], "excluded_lines": []}, "LessEqual": {"executed_lines": [176], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Like": {"executed_lines": [183], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ILike": {"executed_lines": [190], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "In": {"executed_lines": [197], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NotIn": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [204], "excluded_lines": []}, "Contains": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [212, 213], "excluded_lines": []}, "StartsWith": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [221, 222], "excluded_lines": []}, "EndsWith": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [230, 231], "excluded_lines": []}, "LogicalExpression": {"executed_lines": [239], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AndExpression": {"executed_lines": [246, 250, 251, 252, 253, 254], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrExpression": {"executed_lines": [261, 265, 268, 269, 270, 271, 272], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [266], "excluded_lines": []}, "NotExpression": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [279, 283, 284], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 15, 16, 24, 28, 32, 37, 38, 54, 62, 66, 70, 74, 78, 82, 86, 90, 94, 98, 102, 106, 110, 114, 118, 124, 125, 127, 132, 137, 138, 140, 144, 145, 147, 151, 152, 154, 158, 159, 161, 165, 166, 168, 172, 173, 175, 179, 180, 182, 186, 187, 189, 193, 194, 196, 200, 201, 203, 207, 208, 210, 216, 217, 219, 225, 226, 228, 235, 236, 238, 242, 243, 245, 248, 257, 258, 260, 263, 275, 276, 278, 281], "summary": {"covered_lines": 65, "num_statements": 65, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/query/filters.py": {"executed_lines": [1, 8, 9, 12, 13, 30, 52, 58, 60, 66, 68, 69, 70, 72, 74, 84, 85, 86, 89, 90, 93, 96, 98, 100, 110, 112, 113, 115, 116, 118, 119, 121, 122, 127, 130, 131, 156, 162, 163, 164, 165, 167, 169, 171, 173, 175, 177, 178, 179, 180, 182, 192, 193, 194, 195, 197, 204, 205, 206, 208, 211, 212, 213, 214, 215, 218, 219, 220, 221, 223, 224, 225, 226, 227, 228, 231, 232, 235, 236, 238, 240], "summary": {"covered_lines": 77, "num_statements": 78, "percent_covered": 98.71794871794872, "percent_covered_display": "99", "missing_lines": 1, "excluded_lines": 15}, "missing_lines": [124], "excluded_lines": [240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254], "functions": {"FilterExpression.__init__": {"executed_lines": [58], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FilterExpression.to_domain": {"executed_lines": [66, 68, 69, 70, 72], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FilterExpression._parse_lookup": {"executed_lines": [84, 85, 86, 89, 90, 93, 96, 98], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FilterExpression._process_value": {"executed_lines": [110, 112, 113, 115, 116, 118, 119, 121, 122, 127], "summary": {"covered_lines": 10, "num_statements": 11, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [124], "excluded_lines": []}, "Q.__init__": {"executed_lines": [162, 163, 164, 165], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Q.__and__": {"executed_lines": [169], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Q.__or__": {"executed_lines": [173], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Q.__invert__": {"executed_lines": [177, 178, 179, 180], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Q._combine": {"executed_lines": [192, 193, 194, 195], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Q.to_domain": {"executed_lines": [204, 205, 206, 208, 211, 212, 213, 214, 215, 218, 219, 220, 221, 223, 224, 225, 226, 227, 228, 231, 232, 235, 236, 238], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Q.__repr__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 14}, "missing_lines": [], "excluded_lines": [241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254]}, "": {"executed_lines": [1, 8, 9, 12, 13, 30, 52, 60, 74, 100, 130, 131, 156, 167, 171, 175, 182, 197, 240], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [240]}}, "classes": {"FilterExpression": {"executed_lines": [58, 66, 68, 69, 70, 72, 84, 85, 86, 89, 90, 93, 96, 98, 110, 112, 113, 115, 116, 118, 119, 121, 122, 127], "summary": {"covered_lines": 24, "num_statements": 25, "percent_covered": 96.0, "percent_covered_display": "96", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [124], "excluded_lines": []}, "Q": {"executed_lines": [162, 163, 164, 165, 169, 173, 177, 178, 179, 180, 192, 193, 194, 195, 204, 205, 206, 208, 211, 212, 213, 214, 215, 218, 219, 220, 221, 223, 224, 225, 226, 227, 228, 231, 232, 235, 236, 238], "summary": {"covered_lines": 38, "num_statements": 38, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 14}, "missing_lines": [], "excluded_lines": [241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254]}, "": {"executed_lines": [1, 8, 9, 12, 13, 30, 52, 60, 74, 100, 130, 131, 156, 167, 171, 175, 182, 197, 240], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [240]}}}, "src/zenoo_rpc/query/lazy.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 16, 17, 23, 38, 55, 66, 74, 82, 90, 95, 96, 114, 138, 155, 167, 176, 185, 194, 202, 210, 218, 222, 228, 232, 243, 244, 250, 256, 257, 259, 289, 317, 351], "summary": {"covered_lines": 34, "num_statements": 127, "percent_covered": 26.771653543307085, "percent_covered_display": "27", "missing_lines": 93, "excluded_lines": 9}, "missing_lines": [31, 32, 33, 34, 35, 36, 44, 45, 48, 49, 52, 53, 57, 58, 59, 60, 61, 63, 64, 72, 80, 84, 85, 86, 87, 88, 92, 129, 130, 131, 132, 134, 135, 136, 144, 145, 148, 149, 152, 153, 157, 158, 159, 160, 161, 162, 164, 165, 173, 174, 182, 183, 191, 192, 200, 208, 212, 213, 214, 215, 216, 220, 224, 225, 226, 230, 272, 273, 276, 277, 278, 279, 280, 281, 284, 285, 305, 306, 309, 311, 312, 313, 331, 333, 334, 335, 336, 338, 341, 343, 344, 349, 353], "excluded_lines": [232, 233, 234, 235, 236, 237, 238, 239, 240], "functions": {"LazyLoader.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [31, 32, 33, 34, 35, 36], "excluded_lines": []}, "LazyLoader.load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [44, 45, 48, 49, 52, 53], "excluded_lines": []}, "LazyLoader._do_load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [57, 58, 59, 60, 61, 63, 64], "excluded_lines": []}, "LazyLoader.is_loaded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [72], "excluded_lines": []}, "LazyLoader.get_cached_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [80], "excluded_lines": []}, "LazyLoader.invalidate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [84, 85, 86, 87, 88], "excluded_lines": []}, "LazyLoader.__await__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [92], "excluded_lines": []}, "LazyCollection.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [129, 130, 131, 132, 134, 135, 136], "excluded_lines": []}, "LazyCollection.all": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [144, 145, 148, 149, 152, 153], "excluded_lines": []}, "LazyCollection._load_all": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [157, 158, 159, 160, 161, 162, 164, 165], "excluded_lines": []}, "LazyCollection.first": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [173, 174], "excluded_lines": []}, "LazyCollection.count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [182, 183], "excluded_lines": []}, "LazyCollection.exists": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [191, 192], "excluded_lines": []}, "LazyCollection.is_loaded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [200], "excluded_lines": []}, "LazyCollection.get_cached_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [208], "excluded_lines": []}, "LazyCollection.invalidate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 215, 216], "excluded_lines": []}, "LazyCollection.__aiter__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [220], "excluded_lines": []}, "LazyCollection._async_iterator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [224, 225, 226], "excluded_lines": []}, "LazyCollection.__await__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [230], "excluded_lines": []}, "LazyCollection.__repr__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 8}, "missing_lines": [], "excluded_lines": [233, 234, 235, 236, 237, 238, 239, 240]}, "PrefetchManager.__init__": {"executed_lines": [256, 257], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PrefetchManager.prefetch_related": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [272, 273, 276, 277, 278, 279, 280, 281, 284, 285], "excluded_lines": []}, "PrefetchManager._prefetch_for_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [305, 306, 309, 311, 312, 313], "excluded_lines": []}, "PrefetchManager._prefetch_relationship_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [331, 333, 334, 335, 336, 338, 341, 343, 344, 349], "excluded_lines": []}, "PrefetchManager.clear_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [353], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 16, 17, 23, 38, 55, 66, 74, 82, 90, 95, 96, 114, 138, 155, 167, 176, 185, 194, 202, 210, 218, 222, 228, 232, 243, 244, 250, 259, 289, 317, 351], "summary": {"covered_lines": 32, "num_statements": 32, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [232]}}, "classes": {"LazyLoader": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [31, 32, 33, 34, 35, 36, 44, 45, 48, 49, 52, 53, 57, 58, 59, 60, 61, 63, 64, 72, 80, 84, 85, 86, 87, 88, 92], "excluded_lines": []}, "LazyCollection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 8}, "missing_lines": [129, 130, 131, 132, 134, 135, 136, 144, 145, 148, 149, 152, 153, 157, 158, 159, 160, 161, 162, 164, 165, 173, 174, 182, 183, 191, 192, 200, 208, 212, 213, 214, 215, 216, 220, 224, 225, 226, 230], "excluded_lines": [233, 234, 235, 236, 237, 238, 239, 240]}, "PrefetchManager": {"executed_lines": [256, 257], "summary": {"covered_lines": 2, "num_statements": 29, "percent_covered": 6.896551724137931, "percent_covered_display": "7", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [272, 273, 276, 277, 278, 279, 280, 281, 284, 285, 305, 306, 309, 311, 312, 313, 331, 333, 334, 335, 336, 338, 341, 343, 344, 349, 353], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 16, 17, 23, 38, 55, 66, 74, 82, 90, 95, 96, 114, 138, 155, 167, 176, 185, 194, 202, 210, 218, 222, 228, 232, 243, 244, 250, 259, 289, 317, 351], "summary": {"covered_lines": 32, "num_statements": 32, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [232]}}}, "src/zenoo_rpc/retry/__init__.py": {"executed_lines": [1, 8, 15, 16, 17, 26], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 15, 16, 17, 26], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 15, 16, 17, 26], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/retry/decorators.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 14, 15, 17, 19, 22, 45, 46, 49, 50, 51, 52, 53, 55, 57, 58, 60, 62, 63, 64, 65, 66, 68, 69, 71, 72, 75, 78, 80, 81, 82, 85, 89, 98, 99, 102, 103, 106, 107, 109, 116, 117, 120, 124, 126, 127, 130, 153, 154, 157, 158, 159, 160, 161, 163, 165, 170, 171, 172, 173, 174, 176, 177, 179, 180, 183, 186, 188, 189, 190, 193, 197, 206, 207, 210, 211, 214, 220, 227, 228, 231, 232, 235, 237, 238, 242, 259, 276], "summary": {"covered_lines": 93, "num_statements": 127, "percent_covered": 73.22834645669292, "percent_covered_display": "73", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [54, 56, 59, 76, 86, 121, 162, 164, 166, 167, 168, 184, 194, 215, 216, 218, 248, 250, 251, 252, 253, 254, 256, 265, 267, 268, 269, 270, 271, 273, 278, 280, 281, 283], "excluded_lines": [], "functions": {"retry": {"executed_lines": [45, 46, 49, 50, 51, 52, 53, 55, 57, 58, 60, 62, 63, 127], "summary": {"covered_lines": 14, "num_statements": 17, "percent_covered": 82.3529411764706, "percent_covered_display": "82", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [54, 56, 59], "excluded_lines": []}, "retry.decorator": {"executed_lines": [64, 126], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "retry.decorator.wrapper": {"executed_lines": [65, 66, 68, 69, 71, 72, 75, 78, 80, 81, 82, 85, 89, 98, 99, 102, 103, 106, 107, 109, 116, 117, 120, 124], "summary": {"covered_lines": 24, "num_statements": 27, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [76, 86, 121], "excluded_lines": []}, "async_retry": {"executed_lines": [153, 154, 157, 158, 159, 160, 161, 163, 165, 170, 171, 238], "summary": {"covered_lines": 12, "num_statements": 17, "percent_covered": 70.58823529411765, "percent_covered_display": "71", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [162, 164, 166, 167, 168], "excluded_lines": []}, "async_retry.decorator": {"executed_lines": [172, 237], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "async_retry.decorator.wrapper": {"executed_lines": [173, 174, 176, 177, 179, 180, 183, 186, 188, 189, 190, 193, 197, 206, 207, 210, 211, 214, 220, 227, 228, 231, 232, 235], "summary": {"covered_lines": 24, "num_statements": 29, "percent_covered": 82.75862068965517, "percent_covered_display": "83", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [184, 194, 215, 216, 218], "excluded_lines": []}, "network_retry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [248, 250, 251, 252, 253, 254, 256], "excluded_lines": []}, "database_retry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [265, 267, 268, 269, 270, 271, 273], "excluded_lines": []}, "quick_retry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [278, 280, 281, 283], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 14, 15, 17, 19, 22, 130, 242, 259, 276], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 14, 15, 17, 19, 22, 45, 46, 49, 50, 51, 52, 53, 55, 57, 58, 60, 62, 63, 64, 65, 66, 68, 69, 71, 72, 75, 78, 80, 81, 82, 85, 89, 98, 99, 102, 103, 106, 107, 109, 116, 117, 120, 124, 126, 127, 130, 153, 154, 157, 158, 159, 160, 161, 163, 165, 170, 171, 172, 173, 174, 176, 177, 179, 180, 183, 186, 188, 189, 190, 193, 197, 206, 207, 210, 211, 214, 220, 227, 228, 231, 232, 235, 237, 238, 242, 259, 276], "summary": {"covered_lines": 93, "num_statements": 127, "percent_covered": 73.22834645669292, "percent_covered_display": "73", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [54, 56, 59, 76, 86, 121, 162, 164, 166, 167, 168, 184, 194, 215, 216, 218, 248, 250, 251, 252, 253, 254, 256, 265, 267, 268, 269, 270, 271, 273, 278, 280, 281, 283], "excluded_lines": []}}}, "src/zenoo_rpc/retry/exceptions.py": {"executed_lines": [1, 5, 8, 9, 10, 13, 14, 16, 17, 18, 19, 25, 26, 28, 29, 30, 31], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"MaxRetriesExceededError.__init__": {"executed_lines": [17, 18, 19], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RetryTimeoutError.__init__": {"executed_lines": [29, 30, 31], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 10, 13, 14, 16, 25, 26, 28], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RetryError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MaxRetriesExceededError": {"executed_lines": [17, 18, 19], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RetryTimeoutError": {"executed_lines": [29, 30, 31], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 10, 13, 14, 16, 25, 26, 28], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/retry/policies.py": {"executed_lines": [1, 7, 8, 9, 11, 12, 15, 16, 17, 19, 20, 21, 22, 23, 25, 37, 38, 41, 42, 45, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 61, 62, 64, 66, 75, 78, 79, 81, 83, 90, 98, 112, 113, 115, 116, 119, 130, 134, 148, 150, 153, 158, 161, 162, 164, 195, 196, 198, 211, 212, 214], "summary": {"covered_lines": 54, "num_statements": 67, "percent_covered": 80.59701492537313, "percent_covered_display": "81", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [58, 155, 156, 166, 173, 174, 175, 179, 180, 182, 199, 215, 221], "excluded_lines": [], "functions": {"RetryPolicy.should_retry": {"executed_lines": [37, 38, 41, 42, 45, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 61, 62, 64], "summary": {"covered_lines": 18, "num_statements": 19, "percent_covered": 94.73684210526316, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [58], "excluded_lines": []}, "RetryPolicy.get_delay": {"executed_lines": [75], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultRetryPolicy.__init__": {"executed_lines": [83, 90, 98], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NetworkRetryPolicy.__init__": {"executed_lines": [116, 119, 130, 134], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NetworkRetryPolicy._network_retry_condition": {"executed_lines": [150, 153, 158], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [155, 156], "excluded_lines": []}, "DatabaseRetryPolicy.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [166, 173, 174, 175, 179, 180, 182], "excluded_lines": []}, "QuickRetryPolicy.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [199], "excluded_lines": []}, "AggressiveRetryPolicy.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [215, 221], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 12, 15, 16, 17, 19, 20, 21, 22, 23, 25, 66, 78, 79, 81, 112, 113, 115, 148, 161, 162, 164, 195, 196, 198, 211, 212, 214], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RetryPolicy": {"executed_lines": [37, 38, 41, 42, 45, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 61, 62, 64, 75], "summary": {"covered_lines": 19, "num_statements": 20, "percent_covered": 95.0, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [58], "excluded_lines": []}, "DefaultRetryPolicy": {"executed_lines": [83, 90, 98], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NetworkRetryPolicy": {"executed_lines": [116, 119, 130, 134, 150, 153, 158], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [155, 156], "excluded_lines": []}, "DatabaseRetryPolicy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [166, 173, 174, 175, 179, 180, 182], "excluded_lines": []}, "QuickRetryPolicy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [199], "excluded_lines": []}, "AggressiveRetryPolicy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [215, 221], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 11, 12, 15, 16, 17, 19, 20, 21, 22, 23, 25, 66, 78, 79, 81, 112, 113, 115, 148, 161, 162, 164, 195, 196, 198, 211, 212, 214], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/retry/strategies.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 26, 27, 31, 32, 34, 47, 48, 49, 51, 52, 63, 72, 75, 78, 80, 81, 82, 84, 86, 96, 99, 100, 102, 119, 120, 121, 123, 125, 128, 129, 131, 148, 149, 150, 152, 154, 157, 158, 160, 173, 174, 176, 178, 181, 182, 184, 201, 202, 203, 206, 207, 209, 211, 213, 215, 218, 220, 222, 224, 225, 226, 228, 230, 231], "summary": {"covered_lines": 73, "num_statements": 76, "percent_covered": 96.05263157894737, "percent_covered_display": "96", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [28, 61, 232], "excluded_lines": [], "functions": {"RetryAttempt.duration": {"executed_lines": [26, 27], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [28], "excluded_lines": []}, "RetryStrategy.__init__": {"executed_lines": [47, 48, 49], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RetryStrategy.calculate_delay": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [61], "excluded_lines": []}, "RetryStrategy.get_delay": {"executed_lines": [72, 75, 78, 80, 81, 82, 84], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RetryStrategy.should_retry": {"executed_lines": [96], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ExponentialBackoffStrategy.__init__": {"executed_lines": [119, 120, 121], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ExponentialBackoffStrategy.calculate_delay": {"executed_lines": [125], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LinearBackoffStrategy.__init__": {"executed_lines": [148, 149, 150], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LinearBackoffStrategy.calculate_delay": {"executed_lines": [154], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FixedDelayStrategy.__init__": {"executed_lines": [173, 174], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FixedDelayStrategy.calculate_delay": {"executed_lines": [178], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdaptiveStrategy.__init__": {"executed_lines": [201, 202, 203, 206, 207], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdaptiveStrategy.calculate_delay": {"executed_lines": [211, 213, 215, 218, 220], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdaptiveStrategy.get_success_rate": {"executed_lines": [224, 225, 226], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdaptiveStrategy.record_attempt": {"executed_lines": [230, 231], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [232], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 31, 32, 34, 51, 52, 63, 86, 99, 100, 102, 123, 128, 129, 131, 152, 157, 158, 160, 176, 181, 182, 184, 209, 222, 228], "summary": {"covered_lines": 34, "num_statements": 34, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RetryAttempt": {"executed_lines": [26, 27], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [28], "excluded_lines": []}, "RetryStrategy": {"executed_lines": [47, 48, 49, 72, 75, 78, 80, 81, 82, 84, 96], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [61], "excluded_lines": []}, "ExponentialBackoffStrategy": {"executed_lines": [119, 120, 121, 125], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LinearBackoffStrategy": {"executed_lines": [148, 149, 150, 154], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FixedDelayStrategy": {"executed_lines": [173, 174, 178], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdaptiveStrategy": {"executed_lines": [201, 202, 203, 206, 207, 211, 213, 215, 218, 220, 224, 225, 226, 230, 231], "summary": {"covered_lines": 15, "num_statements": 16, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [232], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 31, 32, 34, 51, 52, 63, 86, 99, 100, 102, 123, 128, 129, 131, 152, 157, 158, 160, 176, 181, 182, 184, 209, 222, 228], "summary": {"covered_lines": 34, "num_statements": 34, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/transaction/__init__.py": {"executed_lines": [1, 8, 9, 10, 12], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 12], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 12], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/transaction/context.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 18, 19, 47, 50, 54, 57, 94, 95, 96, 98, 99, 101, 102, 103, 104, 106, 109, 116, 122, 123, 125, 128, 130, 131, 132, 138, 139, 157, 163, 164, 166, 167, 181, 185, 186, 187, 189, 191, 197, 200, 201, 220, 231, 239, 251, 264, 265], "summary": {"covered_lines": 51, "num_statements": 71, "percent_covered": 71.83098591549296, "percent_covered_display": "72", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [48, 107, 110, 135, 227, 228, 229, 233, 234, 236, 237, 241, 243, 244, 245, 247, 249, 257, 258, 260], "excluded_lines": [], "functions": {"transaction": {"executed_lines": [47, 50, 54], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [48], "excluded_lines": []}, "atomic": {"executed_lines": [94, 95, 128, 130, 131, 132], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [135], "excluded_lines": []}, "atomic.decorator": {"executed_lines": [96, 125], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "atomic.decorator.wrapper": {"executed_lines": [98, 99, 101, 102, 103, 104, 106, 109, 116, 122, 123], "summary": {"covered_lines": 11, "num_statements": 13, "percent_covered": 84.61538461538461, "percent_covered_display": "85", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [107, 110], "excluded_lines": []}, "TransactionContext.__init__": {"executed_lines": [163, 164], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionContext.begin": {"executed_lines": [181, 185, 186, 187, 189], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionContext.get_current_transaction": {"executed_lines": [197], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SavepointContext.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [227, 228, 229], "excluded_lines": []}, "SavepointContext.__aenter__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [233, 234, 236, 237], "excluded_lines": []}, "SavepointContext.__aexit__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [241, 243, 244, 245, 247, 249], "excluded_lines": []}, "SavepointContext.rollback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [257, 258, 260], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 18, 19, 57, 138, 139, 157, 166, 167, 191, 200, 201, 220, 231, 239, 251, 264, 265], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TransactionContext": {"executed_lines": [163, 164, 181, 185, 186, 187, 189, 197], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SavepointContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [227, 228, 229, 233, 234, 236, 237, 241, 243, 244, 245, 247, 249, 257, 258, 260], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 18, 19, 47, 50, 54, 57, 94, 95, 96, 98, 99, 101, 102, 103, 104, 106, 109, 116, 122, 123, 125, 128, 130, 131, 132, 138, 139, 157, 166, 167, 191, 200, 201, 220, 231, 239, 251, 264, 265], "summary": {"covered_lines": 43, "num_statements": 47, "percent_covered": 91.48936170212765, "percent_covered_display": "91", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [48, 107, 110, 135], "excluded_lines": []}}}, "src/zenoo_rpc/transaction/exceptions.py": {"executed_lines": [1, 5, 8, 9, 11, 12, 13, 16, 17, 19, 20, 21, 24, 25, 27, 28, 29, 32, 33, 34, 37, 38, 39], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"TransactionError.__init__": {"executed_lines": [12, 13], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionRollbackError.__init__": {"executed_lines": [20, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionCommitError.__init__": {"executed_lines": [28, 29], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 11, 16, 17, 19, 24, 25, 27, 32, 33, 34, 37, 38, 39], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TransactionError": {"executed_lines": [12, 13], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionRollbackError": {"executed_lines": [20, 21], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionCommitError": {"executed_lines": [28, 29], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "NestedTransactionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionStateError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 8, 9, 11, 16, 17, 19, 24, 25, 27, 32, 33, 34, 37, 38, 39], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/transaction/manager.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 22, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 45, 46, 51, 52, 58, 59, 68, 69, 70, 71, 72, 73, 74, 77, 78, 96, 109, 110, 111, 112, 115, 116, 119, 120, 121, 124, 125, 126, 127, 130, 132, 134, 135, 137, 139, 140, 142, 144, 145, 151, 172, 176, 177, 180, 181, 182, 183, 185, 193, 194, 196, 203, 205, 215, 216, 217, 219, 225, 228, 229, 239, 241, 250, 253, 254, 256, 262, 264, 265, 267, 273, 277, 278, 279, 280, 281, 282, 283, 285, 289, 291, 292, 295, 298, 300, 306, 313, 314, 315, 316, 323, 329, 330, 332, 334, 335, 336, 337, 339, 341, 342, 343, 344, 345, 347, 349, 350, 358, 365, 368, 370, 371, 372, 373, 376, 377, 381, 383, 384, 385, 387, 398, 403, 407, 409, 414, 417, 418, 420, 421, 422, 424, 435, 441, 444, 445, 446, 449, 450, 452, 453, 455, 460, 468, 469, 475, 481, 482, 483, 486, 487, 489, 490, 510, 511, 518, 519, 520, 523, 525, 526, 527, 530, 531, 533, 535, 536, 537, 538, 542, 543, 544, 545, 548, 549, 550, 552, 554, 562, 573, 579, 585, 586, 588, 597, 603], "summary": {"covered_lines": 207, "num_statements": 254, "percent_covered": 81.49606299212599, "percent_covered_display": "81", "missing_lines": 47, "excluded_lines": 0}, "missing_lines": [65, 147, 148, 149, 173, 226, 231, 232, 233, 234, 236, 237, 251, 274, 286, 302, 303, 304, 318, 319, 353, 356, 366, 378, 389, 390, 391, 392, 404, 405, 410, 411, 426, 427, 428, 429, 456, 457, 458, 461, 462, 463, 560, 571, 575, 576, 577], "excluded_lines": [], "functions": {"OperationRecord.get_compensating_operation": {"executed_lines": [45, 46, 51, 52, 58, 59], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [65], "excluded_lines": []}, "Transaction.__init__": {"executed_lines": [109, 110, 111, 112, 115, 116, 119, 120, 121, 124, 125, 126, 127, 130, 132], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Transaction.is_active": {"executed_lines": [137], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Transaction.is_nested": {"executed_lines": [142], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Transaction.duration": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [147, 148, 149], "excluded_lines": []}, "Transaction.add_operation": {"executed_lines": [172, 176, 177, 180, 181, 182, 183, 185, 193, 194], "summary": {"covered_lines": 10, "num_statements": 11, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [173], "excluded_lines": []}, "Transaction.set_context": {"executed_lines": [203], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Transaction.get_context": {"executed_lines": [215, 216, 217], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Transaction.get_duration": {"executed_lines": [225, 228, 229, 239], "summary": {"covered_lines": 4, "num_statements": 11, "percent_covered": 36.36363636363637, "percent_covered_display": "36", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [226, 231, 232, 233, 234, 236, 237], "excluded_lines": []}, "Transaction.create_savepoint": {"executed_lines": [250, 253, 254, 256, 262, 264, 265], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [251], "excluded_lines": []}, "Transaction.rollback_to_savepoint": {"executed_lines": [273, 277, 278, 279, 280, 281, 282, 283, 285, 289, 291, 292, 295, 298, 300], "summary": {"covered_lines": 15, "num_statements": 20, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [274, 286, 302, 303, 304], "excluded_lines": []}, "Transaction._execute_rollback_operations": {"executed_lines": [313, 314, 315, 316], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [318, 319], "excluded_lines": []}, "Transaction._execute_compensating_operation": {"executed_lines": [329, 330, 332, 334, 335, 336, 337, 339, 341, 342, 343, 344, 345, 347, 349, 350], "summary": {"covered_lines": 16, "num_statements": 18, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [353, 356], "excluded_lines": []}, "Transaction.commit": {"executed_lines": [365, 368, 370, 371, 372, 373, 376, 377, 381, 383, 384, 385, 387], "summary": {"covered_lines": 13, "num_statements": 19, "percent_covered": 68.42105263157895, "percent_covered_display": "68", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [366, 378, 389, 390, 391, 392], "excluded_lines": []}, "Transaction.rollback": {"executed_lines": [403, 407, 409, 414, 417, 418, 420, 421, 422, 424], "summary": {"covered_lines": 10, "num_statements": 18, "percent_covered": 55.55555555555556, "percent_covered_display": "56", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [404, 405, 410, 411, 426, 427, 428, 429], "excluded_lines": []}, "Transaction._perform_commit": {"executed_lines": [441, 444, 445, 446, 449, 450, 452, 453, 455, 460], "summary": {"covered_lines": 10, "num_statements": 16, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [456, 457, 458, 461, 462, 463], "excluded_lines": []}, "TransactionManager.__init__": {"executed_lines": [481, 482, 483, 486, 487], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionManager.transaction": {"executed_lines": [510, 511, 518, 519, 520, 523, 525, 526, 527, 530, 531, 533, 535, 536, 537, 538, 542, 543, 544, 545, 548, 549, 550, 552], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionManager.get_current_transaction": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [560], "excluded_lines": []}, "TransactionManager.get_transaction": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [571], "excluded_lines": []}, "TransactionManager.rollback_all": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [575, 576, 577], "excluded_lines": []}, "TransactionManager.get_transaction_stats": {"executed_lines": [585, 586, 588], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionManager.get_stats": {"executed_lines": [603], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 22, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 68, 69, 70, 71, 72, 73, 74, 77, 78, 96, 134, 135, 139, 140, 144, 145, 151, 196, 205, 219, 241, 267, 306, 323, 358, 398, 435, 468, 469, 475, 489, 490, 554, 562, 573, 579, 597], "summary": {"covered_lines": 58, "num_statements": 58, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TransactionState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OperationRecord": {"executed_lines": [45, 46, 51, 52, 58, 59], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [65], "excluded_lines": []}, "Savepoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Transaction": {"executed_lines": [109, 110, 111, 112, 115, 116, 119, 120, 121, 124, 125, 126, 127, 130, 132, 137, 142, 172, 176, 177, 180, 181, 182, 183, 185, 193, 194, 203, 215, 216, 217, 225, 228, 229, 239, 250, 253, 254, 256, 262, 264, 265, 273, 277, 278, 279, 280, 281, 282, 283, 285, 289, 291, 292, 295, 298, 300, 313, 314, 315, 316, 329, 330, 332, 334, 335, 336, 337, 339, 341, 342, 343, 344, 345, 347, 349, 350, 365, 368, 370, 371, 372, 373, 376, 377, 381, 383, 384, 385, 387, 403, 407, 409, 414, 417, 418, 420, 421, 422, 424, 441, 444, 445, 446, 449, 450, 452, 453, 455, 460], "summary": {"covered_lines": 110, "num_statements": 151, "percent_covered": 72.8476821192053, "percent_covered_display": "73", "missing_lines": 41, "excluded_lines": 0}, "missing_lines": [147, 148, 149, 173, 226, 231, 232, 233, 234, 236, 237, 251, 274, 286, 302, 303, 304, 318, 319, 353, 356, 366, 378, 389, 390, 391, 392, 404, 405, 410, 411, 426, 427, 428, 429, 456, 457, 458, 461, 462, 463], "excluded_lines": []}, "TransactionManager": {"executed_lines": [481, 482, 483, 486, 487, 510, 511, 518, 519, 520, 523, 525, 526, 527, 530, 531, 533, 535, 536, 537, 538, 542, 543, 544, 545, 548, 549, 550, 552, 585, 586, 588, 603], "summary": {"covered_lines": 33, "num_statements": 38, "percent_covered": 86.84210526315789, "percent_covered_display": "87", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [560, 571, 575, 576, 577], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 22, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 68, 69, 70, 71, 72, 73, 74, 77, 78, 96, 134, 135, 139, 140, 144, 145, 151, 196, 205, 219, 241, 267, 306, 323, 358, 398, 435, 468, 469, 475, 489, 490, 554, 562, 573, 579, 597], "summary": {"covered_lines": 58, "num_statements": 58, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/transport/__init__.py": {"executed_lines": [1, 9, 10, 12], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 9, 10, 12], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 9, 10, 12], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/transport/httpx_transport.py": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 35, 52, 55, 70, 93, 94, 97, 109, 111, 112, 115, 118, 121, 123, 124, 125, 126, 127, 128, 129, 131, 133, 135, 141, 142, 143, 144, 145, 147, 149, 151, 153, 155, 157], "summary": {"covered_lines": 40, "num_statements": 42, "percent_covered": 95.23809523809524, "percent_covered_display": "95", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [119, 132], "excluded_lines": [], "functions": {"AsyncTransport.__init__": {"executed_lines": [52, 55], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AsyncTransport.json_rpc_call": {"executed_lines": [93, 94, 97, 109, 111, 112, 115, 118, 121, 123, 124, 125, 126, 127, 128, 129, 131, 133], "summary": {"covered_lines": 18, "num_statements": 20, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [119, 132], "excluded_lines": []}, "AsyncTransport.health_check": {"executed_lines": [141, 142, 143, 144, 145], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AsyncTransport.close": {"executed_lines": [149], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AsyncTransport.__aenter__": {"executed_lines": [153], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AsyncTransport.__aexit__": {"executed_lines": [157], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 35, 70, 135, 147, 151, 155], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"AsyncTransport": {"executed_lines": [52, 55, 93, 94, 97, 109, 111, 112, 115, 118, 121, 123, 124, 125, 126, 127, 128, 129, 131, 133, 141, 142, 143, 144, 145, 149, 153, 157], "summary": {"covered_lines": 28, "num_statements": 30, "percent_covered": 93.33333333333333, "percent_covered_display": "93", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [119, 132], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 35, 70, 135, 147, 151, 155], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/transport/pool.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 16, 18, 20, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 38, 39, 40, 42, 43, 44, 47, 48, 49, 50, 52, 54, 55, 56, 58, 59, 60, 61, 62, 67, 69, 70, 71, 72, 73, 74, 75, 77, 79, 80, 82, 83, 84, 85, 86, 87, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 102, 103, 106, 107, 109, 110, 111, 114, 115, 116, 117, 118, 119, 120, 122, 124, 125, 127, 129, 131, 135, 137, 138, 140, 141, 143, 145, 149, 150, 152, 154, 159, 160, 188, 211, 212, 213, 214, 215, 216, 217, 218, 221, 222, 223, 226, 227, 230, 242, 243, 246, 253, 255, 257, 260, 261, 264, 267, 268, 269, 270, 273, 274, 276, 277, 279, 282, 288, 295, 307, 308, 310, 311, 313, 319, 323, 324, 326, 328, 330, 332, 338, 339, 340, 341, 343, 344, 346, 360, 362, 367, 379, 380, 382, 384, 385, 386, 388, 389, 391, 392, 397, 399, 400, 401, 403, 405, 408, 439, 441, 442, 443, 445, 447, 450, 474, 476, 479, 480, 483, 484, 485, 486, 489, 490, 492, 495, 496, 497, 501, 503, 509, 512, 521, 522, 523, 524, 526, 533, 536, 537, 539, 545, 546, 548, 550, 551, 553, 554, 556, 558, 559, 561, 563, 566, 568], "summary": {"covered_lines": 216, "num_statements": 288, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [63, 64, 65, 104, 133, 146, 156, 258, 262, 320, 348, 350, 351, 352, 353, 354, 355, 356, 358, 363, 364, 368, 369, 372, 373, 374, 375, 377, 394, 395, 402, 404, 406, 410, 412, 413, 414, 416, 417, 418, 420, 421, 422, 424, 425, 426, 428, 430, 431, 432, 433, 436, 437, 444, 446, 448, 452, 453, 455, 457, 459, 462, 463, 465, 466, 469, 470, 471, 472, 477, 498, 499], "excluded_lines": [], "functions": {"CircuitBreaker.should_allow_request": {"executed_lines": [54, 55, 56, 58, 59, 60, 61, 62], "summary": {"covered_lines": 8, "num_statements": 11, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [63, 64, 65], "excluded_lines": []}, "CircuitBreaker.record_success": {"executed_lines": [69, 70, 71, 72, 73, 74, 75], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CircuitBreaker.record_failure": {"executed_lines": [79, 80, 82, 83, 84, 85, 86, 87], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConnectionStats.average_response_time": {"executed_lines": [102, 103], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [104], "excluded_lines": []}, "ConnectionStats.error_rate": {"executed_lines": [109, 110, 111], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PooledConnection.mark_used": {"executed_lines": [124, 125], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PooledConnection.mark_idle": {"executed_lines": [129], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PooledConnection.mark_unhealthy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [133], "excluded_lines": []}, "PooledConnection.record_request": {"executed_lines": [137, 138, 140, 141], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PooledConnection.is_healthy": {"executed_lines": [145, 149, 150, 152], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [146], "excluded_lines": []}, "PooledConnection.should_health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [156], "excluded_lines": []}, "EnhancedConnectionPool.__init__": {"executed_lines": [211, 212, 213, 214, 215, 216, 217, 218, 221, 222, 223, 226, 227, 230, 242, 243, 246, 253], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnhancedConnectionPool.initialize": {"executed_lines": [257, 260, 261, 264, 267, 268, 269, 270, 273, 274, 276, 277], "summary": {"covered_lines": 12, "num_statements": 14, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [258, 262], "excluded_lines": []}, "EnhancedConnectionPool._create_connection": {"executed_lines": [282, 288, 295, 307, 308, 310, 311], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnhancedConnectionPool.get_connection": {"executed_lines": [319, 323, 324, 326], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [320], "excluded_lines": []}, "EnhancedConnectionPool._acquire_connection": {"executed_lines": [330, 332, 338, 339, 340, 341, 343, 344, 346], "summary": {"covered_lines": 9, "num_statements": 18, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [348, 350, 351, 352, 353, 354, 355, 356, 358], "excluded_lines": []}, "EnhancedConnectionPool._release_connection": {"executed_lines": [362, 367, 379, 380], "summary": {"covered_lines": 4, "num_statements": 13, "percent_covered": 30.76923076923077, "percent_covered_display": "31", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [363, 364, 368, 369, 372, 373, 374, 375, 377], "excluded_lines": []}, "EnhancedConnectionPool._close_connection": {"executed_lines": [384, 385, 386, 388, 389, 391, 392], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [394, 395], "excluded_lines": []}, "EnhancedConnectionPool._health_check_loop": {"executed_lines": [399, 400, 401, 403, 405], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [402, 404, 406], "excluded_lines": []}, "EnhancedConnectionPool._perform_health_checks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [410, 412, 413, 414, 416, 417, 418, 420, 421, 422, 424, 425, 426, 428, 430, 431, 432, 433, 436, 437], "excluded_lines": []}, "EnhancedConnectionPool._cleanup_loop": {"executed_lines": [441, 442, 443, 445, 447], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [444, 446, 448], "excluded_lines": []}, "EnhancedConnectionPool._cleanup_connections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [452, 453, 455, 457, 459, 462, 463, 465, 466, 469, 470, 471, 472], "excluded_lines": []}, "EnhancedConnectionPool.close": {"executed_lines": [476, 479, 480, 483, 484, 485, 486, 489, 490, 492, 495, 496, 497, 501], "summary": {"covered_lines": 14, "num_statements": 17, "percent_covered": 82.3529411764706, "percent_covered_display": "82", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [477, 498, 499], "excluded_lines": []}, "EnhancedConnectionPool.get_stats": {"executed_lines": [509, 512, 521, 522, 523, 524, 526, 533], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConnectionContext.__init__": {"executed_lines": [545, 546], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConnectionContext.__aenter__": {"executed_lines": [550, 551, 553, 554], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConnectionContext.__aexit__": {"executed_lines": [558, 559, 561, 563, 566, 568], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 16, 18, 20, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 38, 39, 40, 42, 43, 44, 47, 48, 49, 50, 52, 67, 77, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 106, 107, 114, 115, 116, 117, 118, 119, 120, 122, 127, 131, 135, 143, 154, 159, 160, 188, 255, 279, 313, 328, 360, 382, 397, 408, 439, 450, 474, 503, 536, 537, 539, 548, 556], "summary": {"covered_lines": 72, "num_statements": 72, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ConnectionState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CircuitBreakerState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CircuitBreaker": {"executed_lines": [54, 55, 56, 58, 59, 60, 61, 62, 69, 70, 71, 72, 73, 74, 75, 79, 80, 82, 83, 84, 85, 86, 87], "summary": {"covered_lines": 23, "num_statements": 26, "percent_covered": 88.46153846153847, "percent_covered_display": "88", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [63, 64, 65], "excluded_lines": []}, "ConnectionStats": {"executed_lines": [102, 103, 109, 110, 111], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [104], "excluded_lines": []}, "PooledConnection": {"executed_lines": [124, 125, 129, 137, 138, 140, 141, 145, 149, 150, 152], "summary": {"covered_lines": 11, "num_statements": 14, "percent_covered": 78.57142857142857, "percent_covered_display": "79", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [133, 146, 156], "excluded_lines": []}, "EnhancedConnectionPool": {"executed_lines": [211, 212, 213, 214, 215, 216, 217, 218, 221, 222, 223, 226, 227, 230, 242, 243, 246, 253, 257, 260, 261, 264, 267, 268, 269, 270, 273, 274, 276, 277, 282, 288, 295, 307, 308, 310, 311, 319, 323, 324, 326, 330, 332, 338, 339, 340, 341, 343, 344, 346, 362, 367, 379, 380, 384, 385, 386, 388, 389, 391, 392, 399, 400, 401, 403, 405, 441, 442, 443, 445, 447, 476, 479, 480, 483, 484, 485, 486, 489, 490, 492, 495, 496, 497, 501, 509, 512, 521, 522, 523, 524, 526, 533], "summary": {"covered_lines": 93, "num_statements": 158, "percent_covered": 58.860759493670884, "percent_covered_display": "59", "missing_lines": 65, "excluded_lines": 0}, "missing_lines": [258, 262, 320, 348, 350, 351, 352, 353, 354, 355, 356, 358, 363, 364, 368, 369, 372, 373, 374, 375, 377, 394, 395, 402, 404, 406, 410, 412, 413, 414, 416, 417, 418, 420, 421, 422, 424, 425, 426, 428, 430, 431, 432, 433, 436, 437, 444, 446, 448, 452, 453, 455, 457, 459, 462, 463, 465, 466, 469, 470, 471, 472, 477, 498, 499], "excluded_lines": []}, "ConnectionContext": {"executed_lines": [545, 546, 550, 551, 553, 554, 558, 559, 561, 563, 566, 568], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 16, 18, 20, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 38, 39, 40, 42, 43, 44, 47, 48, 49, 50, 52, 67, 77, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 106, 107, 114, 115, 116, 117, 118, 119, 120, 122, 127, 131, 135, 143, 154, 159, 160, 188, 255, 279, 313, 328, 360, 382, 397, 408, 439, 450, 474, 503, 536, 537, 539, 548, 556], "summary": {"covered_lines": 72, "num_statements": 72, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/zenoo_rpc/transport/session.py": {"executed_lines": [1, 8, 10, 13, 14, 32, 34, 35, 36, 37, 38, 39, 40, 42, 43, 45, 47, 48, 50, 52, 53, 55, 57, 58, 62, 63, 67, 68, 72, 73, 77, 129, 181, 218, 232, 234, 235, 236, 237, 238, 239], "summary": {"covered_lines": 39, "num_statements": 86, "percent_covered": 45.348837209302324, "percent_covered_display": "45", "missing_lines": 47, "excluded_lines": 0}, "missing_lines": [60, 65, 70, 75, 95, 97, 98, 101, 109, 110, 111, 116, 117, 118, 119, 122, 124, 125, 126, 127, 149, 151, 152, 156, 164, 165, 166, 170, 171, 172, 174, 176, 177, 178, 179, 187, 188, 190, 192, 207, 208, 210, 212, 227, 228, 229, 230], "excluded_lines": [], "functions": {"SessionManager.__init__": {"executed_lines": [34, 35, 36, 37, 38, 39, 40], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SessionManager.is_authenticated": {"executed_lines": [45], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SessionManager.database": {"executed_lines": [50], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SessionManager.uid": {"executed_lines": [55], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SessionManager.username": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [60], "excluded_lines": []}, "SessionManager.password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [65], "excluded_lines": []}, "SessionManager.context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [70], "excluded_lines": []}, "SessionManager.server_version": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [75], "excluded_lines": []}, "SessionManager.authenticate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [95, 97, 98, 101, 109, 110, 111, 116, 117, 118, 119, 122, 124, 125, 126, 127], "excluded_lines": []}, "SessionManager.authenticate_with_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [149, 151, 152, 156, 164, 165, 166, 170, 171, 172, 174, 176, 177, 178, 179], "excluded_lines": []}, "SessionManager._load_user_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [187, 188, 190, 192, 207, 208, 210, 212], "excluded_lines": []}, "SessionManager.get_call_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [227, 228, 229, 230], "excluded_lines": []}, "SessionManager.clear": {"executed_lines": [234, 235, 236, 237, 238, 239], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 10, 13, 14, 32, 42, 43, 47, 48, 52, 53, 57, 58, 62, 63, 67, 68, 72, 73, 77, 129, 181, 218, 232], "summary": {"covered_lines": 23, "num_statements": 23, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SessionManager": {"executed_lines": [34, 35, 36, 37, 38, 39, 40, 45, 50, 55, 234, 235, 236, 237, 238, 239], "summary": {"covered_lines": 16, "num_statements": 63, "percent_covered": 25.396825396825395, "percent_covered_display": "25", "missing_lines": 47, "excluded_lines": 0}, "missing_lines": [60, 65, 70, 75, 95, 97, 98, 101, 109, 110, 111, 116, 117, 118, 119, 122, 124, 125, 126, 127, 149, 151, 152, 156, 164, 165, 166, 170, 171, 172, 174, 176, 177, 178, 179, 187, 188, 190, 192, 207, 208, 210, 212, 227, 228, 229, 230], "excluded_lines": []}, "": {"executed_lines": [1, 8, 10, 13, 14, 32, 42, 43, 47, 48, 52, 53, 57, 58, 62, 63, 67, 68, 72, 73, 77, 129, 181, 218, 232], "summary": {"covered_lines": 23, "num_statements": 23, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}}, "totals": {"covered_lines": 2869, "num_statements": 3954, "percent_covered": 72.5594334850784, "percent_covered_display": "73", "missing_lines": 1085, "excluded_lines": 42}}