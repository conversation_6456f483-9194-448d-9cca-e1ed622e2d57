"""
JSON-RPC error mapping utilities.

This module provides functions to map JSON-RPC error responses from Odoo
to structured Zenoo-RPC exceptions with proper context and debugging information.
"""

from typing import Any, Dict

from .base import (
    AccessError,
    AuthenticationError,
    InternalError,
    MethodNotFoundError,
    ZenooError,
    ValidationError,
)


def map_jsonrpc_error(error_data: Dict[str, Any]) -> ZenooError:
    """Map JSON-RPC error response to appropriate Zenoo-RPC exception.

    Args:
        error_data: The error data from JSON-RPC response

    Returns:
        An appropriate ZenooError subclass instance
        
    Example:
        >>> error = {
        ...     "code": -32601,
        ...     "message": "Method not found",
        ...     "data": {"name": "odoo.exceptions.ValidationError"}
        ... }
        >>> exception = map_jsonrpc_error(error)
        >>> isinstance(exception, MethodNotFoundError)
        True
    """
    error_code = error_data.get("code")
    error_message = error_data.get("message", "Unknown error")
    error_data_dict = error_data.get("data", {})
    
    # Extract additional context
    context = {
        "code": error_code,
        "raw_error": error_data,
    }
    
    # Map standard JSON-RPC error codes
    if error_code == -32700:
        return ValidationError(
            f"Parse error: {error_message}",
            context=context
        )
    elif error_code == -32600:
        return ValidationError(
            f"Invalid request: {error_message}",
            context=context
        )
    elif error_code == -32601:
        return MethodNotFoundError(
            f"Method not found: {error_message}",
            context=context
        )
    elif error_code == -32602:
        return ValidationError(
            f"Invalid params: {error_message}",
            context=context
        )
    elif error_code == -32603:
        return InternalError(
            f"Internal error: {error_message}",
            server_traceback=error_data_dict.get("debug"),
            context=context
        )
    
    # Map Odoo-specific errors based on exception type
    error_type = error_data_dict.get("name", "")
    server_traceback = error_data_dict.get("debug")
    
    if "AccessError" in error_type or "AccessDenied" in error_type:
        return AccessError(
            error_message,
            server_traceback=server_traceback,
            context=context
        )
    elif "ValidationError" in error_type or "UserError" in error_type:
        return ValidationError(
            error_message,
            context=context
        )
    elif "AuthenticationError" in error_type:
        return AuthenticationError(
            error_message,
            context=context
        )
    elif "MissingError" in error_type:
        return ValidationError(
            f"Record not found: {error_message}",
            context=context
        )
    
    # Default to generic ZenooError for unknown error types
    return ZenooError(
        f"Server error: {error_message}",
        context=context
    )


def extract_server_traceback(error_data: Dict[str, Any]) -> str:
    """Extract server traceback from error data.
    
    Args:
        error_data: The error data from JSON-RPC response
        
    Returns:
        Server traceback string or empty string if not available
    """
    return error_data.get("data", {}).get("debug", "")
