"""
Main Zenoo-RPC client implementation.

This module provides the primary interface for interacting with Odoo servers
through the Zenoo-RPC library. It combines transport, session management,
and high-level API features with zen-like simplicity.
"""

from typing import Any, Dict, List, Optional, Type, TypeVar, TYPE_CHECKING

from .exceptions import Authenti<PERSON><PERSON>rror, ZenooError
from .transport import Async<PERSON>ransport, SessionManager

if TYPE_CHECKING:
    from .models.base import OdooModel
    from .models.registry import get_model_class, get_registry
    from .query.builder import QueryBuilder
    from .transaction.manager import TransactionManager
    from .cache.manager import CacheManager
    from .batch.manager import BatchManager

T = TypeVar("T")


class ZenooClient:
    """Main async client for Zenoo-RPC.

    This is the primary interface for interacting with Odoo servers. It provides
    a zen-like, modern async API with type safety, intelligent caching, and
    superior developer experience.

    Features:
    - Async-first design with httpx transport
    - Type-safe operations with Pydantic models
    - Fluent query builder
    - Intelligent caching and batch operations
    - Structured exception handling
    - Transaction management

    Examples:
        >>> # Using full URL
        >>> async with ZenooClient("https://demo.odoo.com") as client:
        ...     await client.login("demo", "admin", "admin")
        ...     partners = await client.model(ResPartner).filter(
        ...         is_company=True
        ...     ).limit(10).all()

        >>> # Using host with parameters
        >>> async with ZenooClient("localhost", port=8069,
        ...                        protocol="http") as client:
        ...     await client.login("mydb", "admin", "password")
        ...     # ... operations ...
    """

    def __init__(
        self,
        host_or_url: str,
        port: Optional[int] = None,
        protocol: Optional[str] = None,
        timeout: float = 30.0,
        verify_ssl: bool = True,
    ):
        """Initialize the OdooFlow client.

        This constructor supports multiple initialization patterns:

        1. Full URL:
            >>> client = OdooFlowClient("https://demo.odoo.com")
            >>> client = OdooFlowClient("http://localhost:8069")

        2. Host with separate parameters:
            >>> client = OdooFlowClient("demo.odoo.com", protocol="https")
            >>> client = OdooFlowClient("localhost", port=8069, protocol="http")

        3. Host only (defaults to http://host:8069):
            >>> client = OdooFlowClient("localhost")

        Args:
            host_or_url: Either a full URL or just the hostname/IP
            port: Port number (auto-detected from URL or defaults to 8069)
            protocol: Protocol ("http" or "https", auto-detected from URL or defaults to "http")
            timeout: Request timeout in seconds
            verify_ssl: Whether to verify SSL certificates
        """
        # Parse the input to determine if it's a URL or just a host
        base_url = self._parse_host_or_url(host_or_url, port, protocol)

        # Store parsed components for reference
        from urllib.parse import urlparse

        parsed = urlparse(base_url)
        self.host = parsed.hostname
        self.port = parsed.port or (443 if parsed.scheme == "https" else 8069)
        self.protocol = parsed.scheme

        # Initialize transport and session manager
        self._transport = AsyncTransport(
            base_url=base_url,
            timeout=timeout,
            verify_ssl=verify_ssl,
        )
        self._session = SessionManager()

        # Phase 3 features - initialized lazily
        self.transaction_manager: Optional["TransactionManager"] = None
        self.cache_manager: Optional["CacheManager"] = None
        self.batch_manager: Optional["BatchManager"] = None

    def _parse_host_or_url(
        self,
        host_or_url: str,
        port: Optional[int] = None,
        protocol: Optional[str] = None,
    ) -> str:
        """Parse host_or_url and return a complete base URL.

        Args:
            host_or_url: Either a full URL or just hostname/IP
            port: Optional port override
            protocol: Optional protocol override

        Returns:
            Complete base URL
        """
        from urllib.parse import urlparse

        # Check if input looks like a URL (has protocol)
        if "://" in host_or_url:
            parsed = urlparse(host_or_url)

            # Use provided overrides or parsed values
            final_protocol = protocol or parsed.scheme
            final_host = parsed.hostname
            final_port = port or parsed.port

            # Set default port if not specified
            if final_port is None:
                final_port = 443 if final_protocol == "https" else 8069

            return f"{final_protocol}://{final_host}:{final_port}"

        else:
            # Input is just a hostname/IP
            final_protocol = protocol or "http"
            final_host = host_or_url
            final_port = port or (443 if final_protocol == "https" else 8069)

            return f"{final_protocol}://{final_host}:{final_port}"

    @property
    def is_authenticated(self) -> bool:
        """Check if the client is authenticated."""
        return self._session.is_authenticated

    @property
    def database(self) -> Optional[str]:
        """Get the current database name."""
        return self._session.database

    @property
    def uid(self) -> Optional[int]:
        """Get the current user ID."""
        return self._session.uid

    @property
    def username(self) -> Optional[str]:
        """Get the current username."""
        return self._session.username

    @property
    def server_version(self) -> Optional[Dict[str, Any]]:
        """Get server version information."""
        return self._session.server_version

    async def login(self, database: str, username: str, password: str) -> None:
        """Authenticate with the Odoo server.

        Args:
            database: Database name to connect to
            username: Username for authentication
            password: Password for authentication

        Raises:
            AuthenticationError: If authentication fails
            ConnectionError: If connection to server fails
        """
        await self._session.authenticate(self._transport, database, username, password)

    async def login_with_api_key(
        self, database: str, username: str, api_key: str
    ) -> None:
        """Authenticate with the Odoo server using API key.

        Args:
            database: Database name to connect to
            username: Username for authentication
            api_key: API key for authentication

        Raises:
            AuthenticationError: If authentication fails
            ConnectionError: If connection to server fails
        """
        await self._session.authenticate_with_api_key(
            self._transport, database, username, api_key
        )

    async def execute_kw(
        self,
        model: str,
        method: str,
        args: List[Any],
        kwargs: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """Execute a method on an Odoo model.

        This is the low-level method for calling Odoo model methods directly.
        Higher-level APIs should be preferred when available.

        Args:
            model: Name of the Odoo model (e.g., "res.partner")
            method: Method name to call (e.g., "search", "read", "write")
            args: Positional arguments for the method
            kwargs: Keyword arguments for the method
            context: Additional context for the call

        Returns:
            The result of the method call

        Raises:
            AuthenticationError: If not authenticated
            ZenooError: If the server returns an error
        """
        if not self.is_authenticated:
            raise AuthenticationError("Not authenticated. Call login() first.")

        # Prepare call context
        call_context = self._session.get_call_context(context)

        # Prepare parameters
        params = {
            "args": [
                self._session.database,
                self._session.uid,
                self._session.password,  # Use stored password
                model,
                method,
                args,
                kwargs or {},
            ]
        }

        # Add context if provided
        if call_context:
            if len(params["args"]) >= 7:
                params["args"][6]["context"] = call_context
            else:
                params["args"].append({"context": call_context})

        # Make the RPC call
        result = await self._transport.json_rpc_call("object", "execute_kw", params)
        return result.get("result")

    async def execute(
        self,
        model: str,
        method: str,
        *args: Any,
        context: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """Execute a method on an Odoo model (simplified interface).

        Args:
            model: Name of the Odoo model
            method: Method name to call
            *args: Arguments for the method
            context: Additional context for the call

        Returns:
            The result of the method call
        """
        return await self.execute_kw(model, method, list(args), context=context)

    async def search_read(
        self,
        model: str,
        domain: List[Any],
        fields: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        order: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Search and read records in a single call (optimized).

        This method combines search and read operations into a single RPC call
        for better performance, which is one of the key improvements over odoorpc.

        Args:
            model: Name of the Odoo model
            domain: Search domain (list of tuples)
            fields: Fields to read (None for all fields)
            limit: Maximum number of records to return
            offset: Number of records to skip
            order: Sort order specification
            context: Additional context for the call

        Returns:
            List of record dictionaries
        """
        kwargs = {}
        if fields is not None:
            kwargs["fields"] = fields
        if limit is not None:
            kwargs["limit"] = limit
        if offset:
            kwargs["offset"] = offset
        if order:
            kwargs["order"] = order

        return await self.execute_kw(
            model, "search_read", [domain], kwargs, context=context
        )

    async def search_count(
        self,
        model: str,
        domain: List[Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> int:
        """Count records matching the domain.

        Args:
            model: Name of the Odoo model
            domain: Search domain (list of tuples)
            context: Optional context for the operation

        Returns:
            Number of records matching the domain

        Raises:
            AuthenticationError: If not authenticated
            ZenooError: If the server returns an error
        """
        return await self.execute_kw(
            model,
            "search_count",
            [domain],
            context=context,
        )

    async def read(
        self,
        model: str,
        ids: List[int],
        fields: Optional[List[str]] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Read records by IDs.

        Args:
            model: Name of the Odoo model
            ids: List of record IDs to read
            fields: List of field names to read (None for all fields)
            context: Optional context for the operation

        Returns:
            List of record data dictionaries

        Raises:
            AuthenticationError: If not authenticated
            ZenooError: If the server returns an error
        """
        kwargs = {}
        if fields:
            kwargs["fields"] = fields

        return await self.execute_kw(
            model,
            "read",
            [ids],
            kwargs,
            context=context,
        )

    async def get_model_fields(
        self,
        model: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Dict[str, Any]]:
        """Get field definitions for a model.

        Args:
            model: Name of the Odoo model
            context: Optional context for the operation

        Returns:
            Dictionary mapping field names to field definitions

        Raises:
            AuthenticationError: If not authenticated
            ZenooError: If the server returns an error
        """
        return await self.execute_kw(
            model,
            "fields_get",
            [],
            context=context,
        )

    async def health_check(self) -> bool:
        """Check if the Odoo server is healthy and reachable.

        Returns:
            True if server is healthy, False otherwise
        """
        return await self._transport.health_check()

    async def get_server_version(self) -> Dict[str, Any]:
        """Get server version information.

        Returns:
            Dictionary containing server version details
        """
        result = await self._transport.json_rpc_call("common", "version", {})
        return result.get("result", {})

    async def list_databases(self) -> List[str]:
        """List available databases on the server.

        Returns:
            List of database names
        """
        result = await self._transport.json_rpc_call("db", "list", {})
        return result.get("result", [])

    async def close(self) -> None:
        """Close the client and clean up resources."""
        await self._transport.close()
        self._session.clear()

    async def __aenter__(self) -> "ZenooClient":
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Async context manager exit."""
        await self.close()

    def model(self, model_class: Type[T]) -> "QueryBuilder[T]":
        """Get a query builder for a model class.

        Args:
            model_class: The model class to query

        Returns:
            QueryBuilder instance for the model

        Example:
            >>> from odooflow.models import ResPartner
            >>>
            >>> # Get query builder
            >>> partners = client.model(ResPartner)
            >>>
            >>> # Build and execute query
            >>> companies = await partners.filter(is_company=True).all()
        """
        if not self.is_authenticated:
            raise AuthenticationError("Not authenticated. Call login() first.")

        from .query.builder import QueryBuilder

        return QueryBuilder(model_class, self)

    async def get_or_create_model(self, model_name: str) -> Type["OdooModel"]:
        """Get or create a model class for the given Odoo model name.

        Args:
            model_name: The Odoo model name (e.g., "res.partner")

        Returns:
            Model class (either registered or dynamically created)
        """
        from .models.registry import get_registry

        registry = get_registry()

        # Check if model is already registered
        model_class = registry.get_model(model_name)
        if model_class:
            return model_class

        # Create dynamic model
        return await registry.create_dynamic_model(model_name, self)

    # Phase 3 Features

    async def setup_transaction_manager(self) -> "TransactionManager":
        """Setup transaction manager for the client.

        Returns:
            TransactionManager instance
        """
        if self.transaction_manager is None:
            from .transaction.manager import TransactionManager

            self.transaction_manager = TransactionManager(self)

        return self.transaction_manager

    async def setup_cache_manager(
        self, backend: str = "memory", **kwargs
    ) -> "CacheManager":
        """Setup cache manager for the client.

        Args:
            backend: Cache backend ("memory" or "redis")
            **kwargs: Backend-specific configuration

        Returns:
            CacheManager instance
        """
        if self.cache_manager is None:
            from .cache.manager import CacheManager

            self.cache_manager = CacheManager()

            if backend == "memory":
                await self.cache_manager.setup_memory_cache(**kwargs)
            elif backend == "redis":
                await self.cache_manager.setup_redis_cache(**kwargs)

        return self.cache_manager

    async def setup_batch_manager(
        self,
        max_chunk_size: int = 100,
        max_concurrency: int = 5,
        timeout: Optional[int] = None,
    ) -> "BatchManager":
        """Setup batch manager for the client.

        Args:
            max_chunk_size: Maximum records per chunk
            max_concurrency: Maximum concurrent operations
            timeout: Operation timeout in seconds

        Returns:
            BatchManager instance
        """
        if self.batch_manager is None:
            from .batch.manager import BatchManager

            self.batch_manager = BatchManager(
                client=self,
                max_chunk_size=max_chunk_size,
                max_concurrency=max_concurrency,
                timeout=timeout,
            )

        return self.batch_manager

    def transaction(self, **kwargs):
        """Create a transaction context manager.

        Args:
            **kwargs: Transaction options

        Returns:
            Transaction context manager
        """
        if self.transaction_manager is None:
            raise ZenooError(
                "Transaction manager not initialized. Call setup_transaction_manager() first."
            )

        return self.transaction_manager.transaction(**kwargs)

    def batch(self, batch_id: Optional[str] = None):
        """Create a batch for operations.

        Args:
            batch_id: Optional batch identifier

        Returns:
            Batch instance
        """
        if self.batch_manager is None:
            raise ZenooError(
                "Batch manager not initialized. Call setup_batch_manager() first."
            )

        return self.batch_manager.create_batch(batch_id)
