"""
Cache decorators for OdooFlow.

This module provides decorators for automatic caching of
function results with configurable TTL and invalidation.
"""

import functools
import hashlib
import json
from typing import Any, Callable, Optional, Union, List, Dict
import asyncio
import logging

from .manager import <PERSON>ache<PERSON>anager
from .keys import <PERSON><PERSON><PERSON><PERSON>, make_cache_key
from .exceptions import CacheError

logger = logging.getLogger(__name__)


def cached(
    ttl: Optional[int] = None,
    key_prefix: Optional[str] = None,
    backend: Optional[str] = None,
    cache_manager: Optional[CacheManager] = None,
    skip_cache: Optional[Callable] = None,
    key_builder: Optional[Callable] = None
):
    """Decorator for caching function results.
    
    This decorator automatically caches the results of async functions
    based on their arguments, with configurable TTL and cache keys.
    
    Args:
        ttl: Time to live in seconds
        key_prefix: Prefix for cache keys
        backend: Cache backend to use
        cache_manager: Cache manager instance
        skip_cache: Function to determine if caching should be skipped
        key_builder: Custom key building function
        
    Example:
        >>> @cached(ttl=300, key_prefix="partner")
        ... async def get_partner_by_id(client, partner_id):
        ...     return await client.model(ResPartner).get(id=partner_id)
        ...
        >>> # First call hits database
        >>> partner = await get_partner_by_id(client, 123)
        >>> 
        >>> # Second call returns cached result
        >>> partner = await get_partner_by_id(client, 123)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get cache manager
            manager = cache_manager
            if manager is None:
                # Try to get from first argument (usually client)
                if args and hasattr(args[0], 'cache_manager'):
                    manager = args[0].cache_manager
                else:
                    logger.warning(f"No cache manager available for {func.__name__}")
                    return await func(*args, **kwargs)
            
            # Check if caching should be skipped
            if skip_cache and skip_cache(*args, **kwargs):
                return await func(*args, **kwargs)
            
            # Build cache key
            if key_builder:
                cache_key = key_builder(*args, **kwargs)
            else:
                cache_key = _build_function_cache_key(
                    func, args, kwargs, key_prefix
                )
            
            # Try to get from cache
            try:
                cached_result = await manager.get(cache_key, backend=backend)
                if cached_result is not None:
                    logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                    return cached_result
            except Exception as e:
                logger.error(f"Cache get error for {func.__name__}: {e}")
            
            # Execute function
            logger.debug(f"Cache miss for {func.__name__}: {cache_key}")
            result = await func(*args, **kwargs)
            
            # Cache result
            try:
                await manager.set(cache_key, result, ttl=ttl, backend=backend)
                logger.debug(f"Cached result for {func.__name__}: {cache_key}")
            except Exception as e:
                logger.error(f"Cache set error for {func.__name__}: {e}")
            
            return result
        
        # Add cache management methods to the function
        wrapper._cache_key_prefix = key_prefix
        wrapper._cache_ttl = ttl
        wrapper._cache_backend = backend
        
        return wrapper
    
    return decorator


def cache_result(
    model: str,
    operation: str,
    ttl: Optional[int] = None,
    backend: Optional[str] = None,
    invalidate_on: Optional[List[str]] = None
):
    """Decorator for caching Odoo operation results.
    
    This decorator is specifically designed for caching Odoo
    model operations with automatic key generation and invalidation.
    
    Args:
        model: Odoo model name
        operation: Operation type (search, read, count, etc.)
        ttl: Time to live in seconds
        backend: Cache backend to use
        invalidate_on: List of operations that should invalidate this cache
        
    Example:
        >>> @cache_result("res.partner", "search", ttl=300)
        ... async def search_partners(client, domain, **kwargs):
        ...     return await client.search_read("res.partner", domain, **kwargs)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get cache manager from first argument (client)
            if not args or not hasattr(args[0], 'cache_manager'):
                logger.warning(f"No cache manager available for {func.__name__}")
                return await func(*args, **kwargs)
            
            manager = args[0].cache_manager
            
            # Build cache key for Odoo operation
            cache_key = make_cache_key(
                model=model,
                operation=operation,
                params=kwargs
            )
            
            # Try to get from cache
            try:
                cached_result = await manager.get(cache_key, backend=backend)
                if cached_result is not None:
                    logger.debug(f"Cache hit for {model}.{operation}: {cache_key}")
                    return cached_result
            except Exception as e:
                logger.error(f"Cache get error for {model}.{operation}: {e}")
            
            # Execute function
            logger.debug(f"Cache miss for {model}.{operation}: {cache_key}")
            result = await func(*args, **kwargs)
            
            # Cache result
            try:
                await manager.set(cache_key, result, ttl=ttl, backend=backend)
                logger.debug(f"Cached result for {model}.{operation}: {cache_key}")
            except Exception as e:
                logger.error(f"Cache set error for {model}.{operation}: {e}")
            
            return result
        
        # Add metadata
        wrapper._cache_model = model
        wrapper._cache_operation = operation
        wrapper._cache_ttl = ttl
        wrapper._cache_backend = backend
        wrapper._invalidate_on = invalidate_on or []
        
        return wrapper
    
    return decorator


def invalidate_cache(
    patterns: Union[str, List[str]],
    backend: Optional[str] = None
):
    """Decorator for cache invalidation after function execution.
    
    This decorator invalidates cache entries matching the specified
    patterns after the decorated function completes successfully.
    
    Args:
        patterns: Cache key patterns to invalidate
        backend: Cache backend to use
        
    Example:
        >>> @invalidate_cache(["res.partner:*", "partner:*"])
        ... async def update_partner(client, partner_id, data):
        ...     return await client.update("res.partner", partner_id, data)
    """
    if isinstance(patterns, str):
        patterns = [patterns]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Execute function first
            result = await func(*args, **kwargs)
            
            # Invalidate cache after successful execution
            if args and hasattr(args[0], 'cache_manager'):
                manager = args[0].cache_manager
                
                try:
                    for pattern in patterns:
                        await manager.invalidate_pattern(pattern, backend=backend)
                        logger.debug(f"Invalidated cache pattern: {pattern}")
                except Exception as e:
                    logger.error(f"Cache invalidation error: {e}")
            
            return result
        
        return wrapper
    
    return decorator


class CacheInvalidator:
    """Context manager for cache invalidation.
    
    This class provides a context manager for invalidating
    cache entries based on the operations performed within the context.
    
    Example:
        >>> async with CacheInvalidator(client.cache_manager, "res.partner") as invalidator:
        ...     partner = await client.create("res.partner", {"name": "Test"})
        ...     invalidator.add_pattern("partner:*")
        ...     # Cache will be invalidated when context exits
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        model: Optional[str] = None,
        backend: Optional[str] = None
    ):
        """Initialize cache invalidator.
        
        Args:
            cache_manager: Cache manager instance
            model: Odoo model name (for automatic pattern generation)
            backend: Cache backend to use
        """
        self.cache_manager = cache_manager
        self.model = model
        self.backend = backend
        self.patterns: List[str] = []
        
        # Add default patterns for model
        if model:
            self.patterns.append(f"{model}:*")
    
    async def __aenter__(self):
        """Enter context manager."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager and invalidate cache."""
        if exc_type is None:  # Only invalidate on success
            await self.invalidate()
    
    def add_pattern(self, pattern: str) -> None:
        """Add a cache pattern to invalidate.
        
        Args:
            pattern: Cache key pattern
        """
        if pattern not in self.patterns:
            self.patterns.append(pattern)
    
    def add_model_pattern(self, model: str) -> None:
        """Add patterns for a specific model.
        
        Args:
            model: Odoo model name
        """
        self.add_pattern(f"{model}:*")
    
    async def invalidate(self) -> int:
        """Invalidate all registered patterns.
        
        Returns:
            Total number of keys invalidated
        """
        total_invalidated = 0
        
        for pattern in self.patterns:
            try:
                count = await self.cache_manager.invalidate_pattern(
                    pattern, backend=self.backend
                )
                total_invalidated += count
                logger.debug(f"Invalidated {count} keys for pattern: {pattern}")
            except Exception as e:
                logger.error(f"Failed to invalidate pattern {pattern}: {e}")
        
        return total_invalidated


def _build_function_cache_key(
    func: Callable,
    args: tuple,
    kwargs: dict,
    prefix: Optional[str] = None
) -> str:
    """Build a cache key for a function call.
    
    Args:
        func: Function being called
        args: Function arguments
        kwargs: Function keyword arguments
        prefix: Optional key prefix
        
    Returns:
        Cache key string
    """
    # Start with function name
    key_parts = [prefix] if prefix else []
    key_parts.append(func.__name__)
    
    # Add arguments hash
    args_data = {
        "args": _serialize_args(args),
        "kwargs": _serialize_kwargs(kwargs)
    }
    
    args_hash = _hash_data(args_data)
    key_parts.append(args_hash)
    
    return ":".join(key_parts)


def _serialize_args(args: tuple) -> List[Any]:
    """Serialize function arguments for hashing.
    
    Args:
        args: Function arguments
        
    Returns:
        Serializable list
    """
    serialized = []
    
    for arg in args:
        if hasattr(arg, '__dict__'):
            # Skip complex objects like clients
            serialized.append(f"<{type(arg).__name__}>")
        else:
            serialized.append(arg)
    
    return serialized


def _serialize_kwargs(kwargs: dict) -> Dict[str, Any]:
    """Serialize function keyword arguments for hashing.
    
    Args:
        kwargs: Function keyword arguments
        
    Returns:
        Serializable dictionary
    """
    serialized = {}
    
    for key, value in kwargs.items():
        if hasattr(value, '__dict__'):
            # Skip complex objects
            serialized[key] = f"<{type(value).__name__}>"
        else:
            serialized[key] = value
    
    return serialized


def _hash_data(data: Any) -> str:
    """Generate a hash for data.
    
    Args:
        data: Data to hash
        
    Returns:
        Hexadecimal hash string (first 8 characters)
    """
    try:
        json_str = json.dumps(data, sort_keys=True, default=str)
        hash_obj = hashlib.sha256(json_str.encode('utf-8'))
        return hash_obj.hexdigest()[:8]
    except Exception:
        # Fallback to string representation
        return hashlib.sha256(str(data).encode('utf-8')).hexdigest()[:8]
