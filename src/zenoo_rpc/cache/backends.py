"""
Cache backend implementations for OdooFlow.

This module provides different cache backends including
in-memory and Redis implementations with async support.
"""

import asyncio
import json
import pickle
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Set
from collections import OrderedDict
import logging

from .exceptions import CacheBackendError, CacheSerializationError, CacheConnectionError
from .keys import Cache<PERSON>ey, validate_cache_key

logger = logging.getLogger(__name__)


class CacheBackend(ABC):
    """Abstract base class for cache backends.
    
    This class defines the interface that all cache backends
    must implement for consistent caching behavior.
    """
    
    @abstractmethod
    async def get(self, key: Union[str, CacheKey]) -> Optional[Any]:
        """Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        pass
    
    @abstractmethod
    async def set(
        self,
        key: Union[str, CacheKey],
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful
        """
        pass
    
    @abstractmethod
    async def delete(self, key: Union[str, CacheKey]) -> bool:
        """Delete a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key existed and was deleted
        """
        pass
    
    @abstractmethod
    async def exists(self, key: Union[str, CacheKey]) -> bool:
        """Check if a key exists in the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key exists
        """
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cached values.
        
        Returns:
            True if successful
        """
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        pass


class MemoryCache(CacheBackend):
    """In-memory cache backend with TTL and LRU support.
    
    This backend stores data in memory with optional TTL
    and LRU eviction policies for memory management.
    
    Features:
    - TTL (Time To Live) support
    - LRU (Least Recently Used) eviction
    - Thread-safe operations
    - Memory usage tracking
    
    Example:
        >>> cache = MemoryCache(max_size=1000, default_ttl=300)
        >>> await cache.set("key1", "value1", ttl=60)
        >>> value = await cache.get("key1")
    """
    
    def __init__(
        self,
        max_size: int = 1000,
        default_ttl: Optional[int] = None,
        cleanup_interval: int = 60
    ):
        """Initialize memory cache.
        
        Args:
            max_size: Maximum number of items to store
            default_ttl: Default TTL in seconds
            cleanup_interval: Cleanup interval in seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cleanup_interval = cleanup_interval
        
        # Storage
        self._data: OrderedDict[str, Any] = OrderedDict()
        self._expiry: Dict[str, float] = {}
        
        # Statistics
        self._hits = 0
        self._misses = 0
        self._sets = 0
        self._deletes = 0
        
        # Cleanup task
        self._cleanup_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
        # Start cleanup task
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start the cleanup task for expired items."""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired())
    
    async def _cleanup_expired(self):
        """Cleanup expired items periodically."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._remove_expired()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Memory cache cleanup error: {e}")
    
    async def _remove_expired(self):
        """Remove expired items from cache."""
        async with self._lock:
            current_time = time.time()
            expired_keys = [
                key for key, expiry in self._expiry.items()
                if expiry <= current_time
            ]
            
            for key in expired_keys:
                self._data.pop(key, None)
                self._expiry.pop(key, None)
            
            if expired_keys:
                logger.debug(f"Memory cache: Removed {len(expired_keys)} expired items")
    
    async def _evict_lru(self):
        """Evict least recently used items if cache is full."""
        while len(self._data) >= self.max_size:
            # Remove oldest item (LRU)
            oldest_key = next(iter(self._data))
            self._data.pop(oldest_key)
            self._expiry.pop(oldest_key, None)
    
    async def get(self, key: Union[str, CacheKey]) -> Optional[Any]:
        """Get a value from the memory cache."""
        key_str = validate_cache_key(key)
        
        async with self._lock:
            # Check if key exists
            if key_str not in self._data:
                self._misses += 1
                return None
            
            # Check if expired
            if key_str in self._expiry:
                if time.time() > self._expiry[key_str]:
                    # Remove expired item
                    self._data.pop(key_str)
                    self._expiry.pop(key_str)
                    self._misses += 1
                    return None
            
            # Move to end (mark as recently used)
            value = self._data.pop(key_str)
            self._data[key_str] = value
            
            self._hits += 1
            return value
    
    async def set(
        self,
        key: Union[str, CacheKey],
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """Set a value in the memory cache."""
        key_str = validate_cache_key(key)
        
        async with self._lock:
            # Evict if necessary
            await self._evict_lru()
            
            # Set value
            self._data[key_str] = value
            
            # Set expiry
            effective_ttl = ttl or self.default_ttl
            if effective_ttl:
                self._expiry[key_str] = time.time() + effective_ttl
            elif key_str in self._expiry:
                # Remove expiry if no TTL
                del self._expiry[key_str]
            
            self._sets += 1
            return True
    
    async def delete(self, key: Union[str, CacheKey]) -> bool:
        """Delete a value from the memory cache."""
        key_str = validate_cache_key(key)
        
        async with self._lock:
            existed = key_str in self._data
            self._data.pop(key_str, None)
            self._expiry.pop(key_str, None)
            
            if existed:
                self._deletes += 1
            
            return existed
    
    async def exists(self, key: Union[str, CacheKey]) -> bool:
        """Check if a key exists in the memory cache."""
        value = await self.get(key)
        return value is not None
    
    async def clear(self) -> bool:
        """Clear all cached values."""
        async with self._lock:
            self._data.clear()
            self._expiry.clear()
            return True
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get memory cache statistics."""
        async with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = (self._hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "backend": "memory",
                "size": len(self._data),
                "max_size": self.max_size,
                "hits": self._hits,
                "misses": self._misses,
                "hit_rate": round(hit_rate, 2),
                "sets": self._sets,
                "deletes": self._deletes,
                "expired_items": len(self._expiry)
            }
    
    async def close(self):
        """Close the cache and cleanup resources."""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass


class RedisCache(CacheBackend):
    """Redis cache backend with async support.
    
    This backend uses Redis for distributed caching with
    support for TTL, serialization, and connection pooling.
    
    Features:
    - Distributed caching
    - JSON and Pickle serialization
    - Connection pooling
    - Automatic reconnection
    - Namespace support
    
    Example:
        >>> cache = RedisCache(url="redis://localhost:6379/0")
        >>> await cache.connect()
        >>> await cache.set("key1", {"data": "value"}, ttl=300)
        >>> value = await cache.get("key1")
    """
    
    def __init__(
        self,
        url: str = "redis://localhost:6379/0",
        namespace: str = "odooflow",
        serializer: str = "json",
        max_connections: int = 10,
        retry_attempts: int = 3
    ):
        """Initialize Redis cache.
        
        Args:
            url: Redis connection URL
            namespace: Cache namespace
            serializer: Serialization method ("json" or "pickle")
            max_connections: Maximum connections in pool
            retry_attempts: Number of retry attempts
        """
        self.url = url
        self.namespace = namespace
        self.serializer = serializer
        self.max_connections = max_connections
        self.retry_attempts = retry_attempts
        
        # Redis client (will be initialized in connect())
        self.redis = None
        self._connected = False
        
        # Statistics
        self._hits = 0
        self._misses = 0
        self._sets = 0
        self._deletes = 0
        self._errors = 0
    
    async def connect(self):
        """Connect to Redis server."""
        try:
            # Import redis here to make it optional
            import aioredis

            # Support both aioredis 1.x and 2.x
            if hasattr(aioredis, 'from_url'):
                # aioredis 2.x
                self.redis = aioredis.from_url(
                    self.url,
                    max_connections=self.max_connections,
                    retry_on_timeout=True,
                    decode_responses=False  # We handle encoding ourselves
                )
            else:
                # aioredis 1.x
                self.redis = await aioredis.create_redis_pool(
                    self.url,
                    maxsize=self.max_connections,
                    encoding=None  # We handle encoding ourselves
                )

            # Test connection
            await self.redis.ping()
            self._connected = True

            logger.info(f"Connected to Redis: {self.url}")

        except ImportError:
            raise CacheBackendError(
                "aioredis is required for Redis cache backend. "
                "Install with: pip install aioredis"
            )
        except Exception as e:
            raise CacheConnectionError(f"Failed to connect to Redis: {e}")
    
    def _make_key(self, key: str) -> str:
        """Create namespaced key."""
        return f"{self.namespace}:{key}"
    
    def _serialize(self, value: Any) -> bytes:
        """Serialize value for storage."""
        try:
            if self.serializer == "json":
                return json.dumps(value, default=str).encode('utf-8')
            elif self.serializer == "pickle":
                return pickle.dumps(value)
            else:
                raise CacheSerializationError(f"Unknown serializer: {self.serializer}")
        except Exception as e:
            raise CacheSerializationError(f"Serialization failed: {e}")
    
    def _deserialize(self, data: bytes) -> Any:
        """Deserialize value from storage."""
        try:
            if self.serializer == "json":
                return json.loads(data.decode('utf-8'))
            elif self.serializer == "pickle":
                return pickle.loads(data)
            else:
                raise CacheSerializationError(f"Unknown serializer: {self.serializer}")
        except Exception as e:
            raise CacheSerializationError(f"Deserialization failed: {e}")
    
    async def _ensure_connected(self):
        """Ensure Redis connection is active."""
        if not self._connected or not self.redis:
            await self.connect()
    
    async def get(self, key: Union[str, CacheKey]) -> Optional[Any]:
        """Get a value from Redis cache."""
        await self._ensure_connected()
        key_str = validate_cache_key(key)
        redis_key = self._make_key(key_str)
        
        try:
            data = await self.redis.get(redis_key)
            if data is None:
                self._misses += 1
                return None
            
            value = self._deserialize(data)
            self._hits += 1
            return value
            
        except Exception as e:
            self._errors += 1
            logger.error(f"Redis cache get error: {e}")
            raise CacheBackendError(f"Failed to get from Redis: {e}")
    
    async def set(
        self,
        key: Union[str, CacheKey],
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """Set a value in Redis cache."""
        await self._ensure_connected()
        key_str = validate_cache_key(key)
        redis_key = self._make_key(key_str)
        
        try:
            data = self._serialize(value)
            
            if ttl:
                await self.redis.setex(redis_key, ttl, data)
            else:
                await self.redis.set(redis_key, data)
            
            self._sets += 1
            return True
            
        except Exception as e:
            self._errors += 1
            logger.error(f"Redis cache set error: {e}")
            raise CacheBackendError(f"Failed to set in Redis: {e}")
    
    async def delete(self, key: Union[str, CacheKey]) -> bool:
        """Delete a value from Redis cache."""
        await self._ensure_connected()
        key_str = validate_cache_key(key)
        redis_key = self._make_key(key_str)
        
        try:
            result = await self.redis.delete(redis_key)
            if result > 0:
                self._deletes += 1
                return True
            return False
            
        except Exception as e:
            self._errors += 1
            logger.error(f"Redis cache delete error: {e}")
            raise CacheBackendError(f"Failed to delete from Redis: {e}")
    
    async def exists(self, key: Union[str, CacheKey]) -> bool:
        """Check if a key exists in Redis cache."""
        await self._ensure_connected()
        key_str = validate_cache_key(key)
        redis_key = self._make_key(key_str)
        
        try:
            result = await self.redis.exists(redis_key)
            return result > 0
            
        except Exception as e:
            self._errors += 1
            logger.error(f"Redis cache exists error: {e}")
            raise CacheBackendError(f"Failed to check existence in Redis: {e}")
    
    async def clear(self) -> bool:
        """Clear all cached values in namespace."""
        await self._ensure_connected()
        
        try:
            # Get all keys in namespace
            pattern = f"{self.namespace}:*"
            keys = await self.redis.keys(pattern)
            
            if keys:
                await self.redis.delete(*keys)
            
            return True
            
        except Exception as e:
            self._errors += 1
            logger.error(f"Redis cache clear error: {e}")
            raise CacheBackendError(f"Failed to clear Redis cache: {e}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics."""
        total_requests = self._hits + self._misses
        hit_rate = (self._hits / total_requests * 100) if total_requests > 0 else 0
        
        stats = {
            "backend": "redis",
            "connected": self._connected,
            "hits": self._hits,
            "misses": self._misses,
            "hit_rate": round(hit_rate, 2),
            "sets": self._sets,
            "deletes": self._deletes,
            "errors": self._errors,
            "namespace": self.namespace,
            "serializer": self.serializer
        }
        
        # Add Redis server info if connected
        if self._connected and self.redis:
            try:
                info = await self.redis.info()
                stats.update({
                    "redis_version": info.get("redis_version"),
                    "used_memory": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients")
                })
            except Exception:
                pass  # Ignore errors getting Redis info
        
        return stats
    
    async def close(self):
        """Close Redis connection."""
        if self.redis:
            try:
                # Support both aioredis 1.x and 2.x
                if hasattr(self.redis, 'wait_closed'):
                    # aioredis 1.x
                    self.redis.close()
                    await self.redis.wait_closed()
                else:
                    # aioredis 2.x
                    await self.redis.close()
            except Exception as e:
                logger.warning(f"Error closing Redis connection: {e}")
            finally:
                self._connected = False
