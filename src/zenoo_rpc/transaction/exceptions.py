"""
Transaction-specific exceptions for Zenoo-RPC.
"""

from ..exceptions import ZenooError


class TransactionError(ZenooError):
    """Base exception for transaction-related errors."""
    
    def __init__(self, message: str, transaction_id: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.transaction_id = transaction_id


class TransactionRollbackError(TransactionError):
    """Exception raised when a transaction rollback fails."""
    
    def __init__(self, message: str, original_error: Exception = None, **kwargs):
        super().__init__(message, **kwargs)
        self.original_error = original_error


class TransactionCommitError(TransactionError):
    """Exception raised when a transaction commit fails."""
    
    def __init__(self, message: str, original_error: Exception = None, **kwargs):
        super().__init__(message, **kwargs)
        self.original_error = original_error


class NestedTransactionError(TransactionError):
    """Exception raised for nested transaction violations."""
    pass


class TransactionStateError(TransactionError):
    """Exception raised when transaction is in invalid state."""
    pass
