"""
Transaction manager implementation for OdooFlow.

This module provides the core transaction management functionality,
including transaction lifecycle, savepoints, and rollback handling.
"""

import asyncio
import uuid
import time
from typing import Any, Dict, List, Optional, Set, Callable, Awaitable
from enum import Enum
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
import logging

from .exceptions import (
    TransactionError, TransactionRollbackError, TransactionCommitError,
    NestedTransactionError, TransactionStateError
)

logger = logging.getLogger(__name__)


class TransactionState(Enum):
    """Transaction state enumeration."""
    ACTIVE = "active"
    COMMITTED = "committed"
    ROLLED_BACK = "rolled_back"
    FAILED = "failed"


@dataclass
class OperationRecord:
    """Record of an operation for rollback purposes."""
    operation_type: str  # 'create', 'update', 'delete'
    model: str
    record_ids: List[int]
    original_data: Optional[Dict[str, Any]] = None
    created_ids: Optional[List[int]] = None
    timestamp: float = field(default_factory=time.time)

    def get_compensating_operation(self) -> Dict[str, Any]:
        """Get the compensating operation to undo this operation."""
        if self.operation_type == 'create':
            return {
                'type': 'delete',
                'model': self.model,
                'ids': self.created_ids or self.record_ids
            }
        elif self.operation_type == 'update':
            return {
                'type': 'update',
                'model': self.model,
                'ids': self.record_ids,
                'values': self.original_data or {}
            }
        elif self.operation_type == 'delete':
            return {
                'type': 'create',
                'model': self.model,
                'values': self.original_data or {}
            }
        else:
            raise ValueError(f"Unknown operation type: {self.operation_type}")


@dataclass
class Savepoint:
    """Represents a transaction savepoint."""
    name: str
    savepoint_id: str
    operation_index: int
    timestamp: float = field(default_factory=time.time)


class Transaction:
    """Represents a single transaction with Odoo.
    
    This class manages the lifecycle of a transaction, including
    operations tracking, commit/rollback, and savepoint management.
    
    Features:
    - Operation tracking for rollback
    - Savepoint support for nested transactions
    - Automatic cleanup on context exit
    - Performance monitoring
    
    Example:
        >>> async with client.transaction() as tx:
        ...     partner = await tx.create(ResPartner, name="Test")
        ...     await tx.update(partner, email="<EMAIL>")
        ...     # Automatic commit on success
    """
    
    def __init__(
        self,
        client: Any,
        transaction_id: Optional[str] = None,
        parent: Optional['Transaction'] = None
    ):
        """Initialize a transaction.
        
        Args:
            client: OdooFlow client instance
            transaction_id: Unique transaction identifier
            parent: Parent transaction for nested transactions
        """
        self.id = transaction_id or str(uuid.uuid4())
        self.client = client
        self.parent = parent
        self.state = TransactionState.ACTIVE
        
        # Operation tracking for rollback
        self.operations: List[OperationRecord] = []
        self.savepoints: List[Savepoint] = []
        
        # Nested transactions
        self.children: Set['Transaction'] = set()
        if parent:
            parent.children.add(self)
        
        # Performance tracking
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.committed_at: Optional[float] = None
        self.rolled_back_at: Optional[float] = None
        
        # Context tracking
        self._context: Dict[str, Any] = {}
        
        logger.debug(f"Transaction {self.id} created")
    
    @property
    def is_active(self) -> bool:
        """Check if transaction is active."""
        return self.state == TransactionState.ACTIVE
    
    @property
    def is_nested(self) -> bool:
        """Check if this is a nested transaction."""
        return self.parent is not None
    
    @property
    def duration(self) -> Optional[float]:
        """Get transaction duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
    
    def add_operation(
        self,
        operation_type: str,
        model: str,
        record_ids: Optional[List[int]] = None,
        original_data: Optional[Dict[str, Any]] = None,
        created_ids: Optional[List[int]] = None,
        data: Optional[Dict[str, Any]] = None,
        record_id: Optional[int] = None
    ) -> None:
        """Add an operation to the transaction log.

        Args:
            operation_type: Type of operation (create, update, delete)
            model: Odoo model name
            record_ids: List of record IDs affected
            original_data: Original data before operation (for rollback)
            created_ids: IDs of created records (for create operations)
            data: Operation data (for create/update operations)
            record_id: Single record ID (converted to record_ids list)
        """
        if not self.is_active:
            raise TransactionStateError(f"Cannot add operation to {self.state.value} transaction")

        # Handle backward compatibility - use data as original_data if provided
        if data and not original_data:
            original_data = data

        # Handle record_id parameter (convert to record_ids list)
        if record_id is not None:
            record_ids = [record_id]
        elif record_ids is None:
            record_ids = []

        operation = OperationRecord(
            operation_type=operation_type,
            model=model,
            record_ids=record_ids,
            original_data=original_data,
            created_ids=created_ids
        )

        self.operations.append(operation)
        logger.debug(f"Transaction {self.id}: Added {operation_type} operation on {model}")

    def set_context(self, key: str, value: Any) -> None:
        """Set context data for the transaction.

        Args:
            key: Context key
            value: Context value
        """
        self._context[key] = value

    def get_context(self, key: str = None, default: Any = None) -> Any:
        """Get context data from the transaction.

        Args:
            key: Context key (if None, returns all context)
            default: Default value if key not found

        Returns:
            Context value, all context, or default
        """
        if key is None:
            return self._context
        return self._context.get(key, default)

    def get_duration(self) -> Optional[float]:
        """Get transaction duration in seconds.

        Returns:
            Duration in seconds or None if not completed
        """
        if self.start_time is None:
            return None

        end_time = self.end_time or self.rolled_back_at or self.committed_at
        if end_time is None:
            # Transaction still active, return current duration
            import asyncio
            try:
                return asyncio.get_event_loop().time() - self.start_time
            except RuntimeError:
                # No event loop, use time.time()
                import time
                return time.time() - self.start_time

        return end_time - self.start_time

    async def create_savepoint(self, name: Optional[str] = None) -> str:
        """Create a savepoint for nested transaction support.

        Args:
            name: Optional savepoint name

        Returns:
            Savepoint identifier
        """
        if not self.is_active:
            raise TransactionStateError(f"Cannot create savepoint in {self.state.value} transaction")

        savepoint_name = name or f"sp_{len(self.savepoints)}"
        savepoint_id = str(uuid.uuid4())

        savepoint = Savepoint(
            name=savepoint_name,
            savepoint_id=savepoint_id,
            operation_index=len(self.operations)
        )

        self.savepoints.append(savepoint)

        logger.debug(f"Transaction {self.id}: Created savepoint {savepoint_name}")
        return savepoint_id

    async def rollback_to_savepoint(self, savepoint_id: str) -> None:
        """Rollback to a specific savepoint.

        Args:
            savepoint_id: Savepoint identifier to rollback to
        """
        if not self.is_active:
            raise TransactionStateError(f"Cannot rollback in {self.state.value} transaction")

        # Find the savepoint
        savepoint = None
        savepoint_index = -1
        for i, sp in enumerate(self.savepoints):
            if sp.savepoint_id == savepoint_id:
                savepoint = sp
                savepoint_index = i
                break

        if savepoint is None:
            raise TransactionError(f"Savepoint {savepoint_id} not found")

        # Rollback operations after the savepoint
        operations_to_rollback = self.operations[savepoint.operation_index:]

        try:
            await self._execute_rollback_operations(operations_to_rollback)

            # Remove operations after savepoint
            self.operations = self.operations[:savepoint.operation_index]

            # Remove savepoints after this one
            self.savepoints = self.savepoints[:savepoint_index + 1]

            logger.info(f"Transaction {self.id}: Rolled back to savepoint {savepoint.name}")

        except Exception as e:
            logger.error(f"Transaction {self.id}: Failed to rollback to savepoint: {e}")
            raise TransactionRollbackError(f"Failed to rollback to savepoint: {e}") from e
    
    async def _execute_rollback_operations(self, operations: List[OperationRecord]) -> None:
        """Execute compensating operations to rollback changes.

        Args:
            operations: List of operations to rollback (in reverse order)
        """
        failed_operations = []
        # Process operations in reverse order
        for operation in reversed(operations):
            try:
                compensating_op = operation.get_compensating_operation()
                await self._execute_compensating_operation(compensating_op)

            except Exception as e:
                logger.error(f"Failed to rollback operation {operation.operation_type} on {operation.model}: {e}")
                failed_operations.append((operation, e))
                # Continue with other operations, but track the failure
        
        # If any operations failed, raise an error
        if failed_operations:
            error_msg = f"Failed to rollback {len(failed_operations)} operations"
            raise TransactionRollbackError(error_msg)

    async def _execute_compensating_operation(self, operation: Dict[str, Any]) -> None:
        """Execute a single compensating operation.

        Args:
            operation: Compensating operation to execute
        """
        op_type = operation['type']
        model = operation['model']

        if op_type == 'delete':
            # Delete records that were created
            ids = operation['ids']
            if ids:
                await self.client.unlink(model, ids)
                logger.debug(f"Rolled back create operation: deleted {len(ids)} records from {model}")

        elif op_type == 'update':
            # Restore original values
            ids = operation['ids']
            values = operation['values']
            if ids and values:
                await self.client.write(model, ids, values)
                logger.debug(f"Rolled back update operation: restored {len(ids)} records in {model}")

        elif op_type == 'create':
            # Recreate deleted records
            values = operation['values']
            if values:
                # Note: This is complex because we need to handle relationships
                # For now, we'll just log that we can't fully restore deleted records
                logger.warning(f"Cannot fully restore deleted records in {model} - data loss may occur")

        else:
            logger.warning(f"Unknown compensating operation type: {op_type}")
    
    async def commit(self) -> None:
        """Commit the transaction.
        
        This method commits all operations in the transaction.
        For nested transactions, this only marks the transaction as committed;
        the actual commit happens when the root transaction commits.
        """
        if not self.is_active:
            raise TransactionStateError(f"Cannot commit {self.state.value} transaction")
        
        try:
            # For nested transactions, just mark as committed
            if self.is_nested:
                self.state = TransactionState.COMMITTED
                logger.debug(f"Nested transaction {self.id} marked as committed")
                return
            
            # Commit all child transactions first
            for child in self.children:
                if child.is_active:
                    await child.commit()
            
            # Perform actual commit operations
            await self._perform_commit()
            
            self.state = TransactionState.COMMITTED
            self.end_time = asyncio.get_event_loop().time()
            self.committed_at = self.end_time
            
            logger.info(f"Transaction {self.id} committed successfully with {len(self.operations)} operations")
            
        except Exception as e:
            self.state = TransactionState.FAILED
            logger.error(f"Transaction {self.id} commit failed: {e}")
            raise TransactionCommitError(
                f"Failed to commit transaction {self.id}",
                original_error=e,
                transaction_id=self.id
            )
    
    async def rollback(self) -> None:
        """Rollback the transaction.
        
        This method undoes all operations in the transaction.
        """
        if self.state in (TransactionState.COMMITTED, TransactionState.ROLLED_BACK):
            logger.warning(f"Transaction {self.id} already {self.state.value}")
            return
        
        try:
            # Rollback all child transactions first
            for child in self.children:
                if child.is_active:
                    await child.rollback()
            
            # Rollback operations using compensating operations
            await self._execute_rollback_operations(self.operations)

            # Clear operations after successful rollback
            self.operations.clear()
            self.savepoints.clear()

            self.state = TransactionState.ROLLED_BACK
            self.end_time = asyncio.get_event_loop().time()
            self.rolled_back_at = self.end_time

            logger.info(f"Transaction {self.id} rolled back successfully")
            
        except Exception as e:
            self.state = TransactionState.FAILED
            logger.error(f"Transaction {self.id} rollback failed: {e}")
            raise TransactionRollbackError(
                f"Failed to rollback transaction {self.id}",
                original_error=e,
                transaction_id=self.id
            )
    
    async def _perform_commit(self) -> None:
        """Perform the actual commit operations."""
        # In a real implementation, this would batch operations
        # and send them to Odoo in an optimized way
        
        # For now, we just log the operations that would be committed
        logger.debug(f"Transaction {self.id}: Committing {len(self.operations)} operations")
        
        # Group operations by type for batch processing
        creates = [op for op in self.operations if op.operation_type == "create"]
        updates = [op for op in self.operations if op.operation_type == "update"]
        deletes = [op for op in self.operations if op.operation_type == "delete"]
        
        # Process in order: creates, updates, deletes
        if creates:
            logger.debug(f"Transaction {self.id}: Processing {len(creates)} create operations")
            # For now, just log the operations - actual implementation would execute them
            for op in creates:
                logger.debug(f"Create operation: {op.model} with {len(op.record_ids)} records")

        if updates:
            logger.debug(f"Transaction {self.id}: Processing {len(updates)} update operations")
            for op in updates:
                logger.debug(f"Update operation: {op.model} with {len(op.record_ids)} records")

        if deletes:
            logger.debug(f"Transaction {self.id}: Processing {len(deletes)} delete operations")
            for op in deletes:
                logger.debug(f"Delete operation: {op.model} with {len(op.record_ids)} records")
    



class TransactionManager:
    """Manages transactions for an OdooFlow client.
    
    This class provides transaction management capabilities,
    including nested transactions, savepoints, and batch operations.
    """
    
    def __init__(self, client: Any):
        """Initialize the transaction manager.
        
        Args:
            client: OdooFlow client instance
        """
        self.client = client
        self.active_transactions: Dict[str, Transaction] = {}
        self.current_transaction: Optional[Transaction] = None

        # Statistics tracking
        self.successful_transactions = 0
        self.failed_transactions = 0
    
    @asynccontextmanager
    async def transaction(
        self,
        transaction_id: Optional[str] = None,
        auto_commit: bool = True
    ):
        """Create a new transaction context.
        
        Args:
            transaction_id: Optional transaction identifier
            auto_commit: Whether to auto-commit on success
            
        Yields:
            Transaction instance
            
        Example:
            >>> async with client.transaction_manager.transaction() as tx:
            ...     await tx.create(ResPartner, name="Test")
            ...     # Auto-commit on success
        """
        # Create transaction
        parent = self.current_transaction
        transaction = Transaction(
            client=self.client,
            transaction_id=transaction_id,
            parent=parent
        )
        
        # Set as current transaction
        previous_transaction = self.current_transaction
        self.current_transaction = transaction
        self.active_transactions[transaction.id] = transaction
        
        # Start timing
        transaction.start_time = asyncio.get_event_loop().time()
        
        try:
            logger.info(f"Starting transaction {transaction.id}")
            yield transaction
            
            # Auto-commit if enabled and no exceptions
            if auto_commit and transaction.is_active:
                await transaction.commit()
                
        except Exception as e:
            # Auto-rollback on exception
            if transaction.is_active:
                logger.warning(f"Transaction {transaction.id} failed, rolling back: {e}")
                await transaction.rollback()
            raise
            
        finally:
            # Track transaction outcome
            if transaction.state == TransactionState.COMMITTED:
                self.successful_transactions += 1
            elif transaction.state in [TransactionState.ROLLED_BACK, TransactionState.FAILED]:
                self.failed_transactions += 1

            # Cleanup
            self.current_transaction = previous_transaction
            if transaction.id in self.active_transactions:
                del self.active_transactions[transaction.id]

            logger.debug(f"Transaction {transaction.id} context exited")
    
    def get_current_transaction(self) -> Optional[Transaction]:
        """Get the current active transaction.
        
        Returns:
            Current transaction or None
        """
        return self.current_transaction
    
    def get_transaction(self, transaction_id: str) -> Optional[Transaction]:
        """Get a transaction by ID.
        
        Args:
            transaction_id: Transaction identifier
            
        Returns:
            Transaction instance or None
        """
        return self.active_transactions.get(transaction_id)
    
    async def rollback_all(self) -> None:
        """Rollback all active transactions."""
        for transaction in list(self.active_transactions.values()):
            if transaction.is_active:
                await transaction.rollback()

    def get_transaction_stats(self) -> Dict[str, Any]:
        """Get statistics about active transactions.

        Returns:
            Dictionary with transaction statistics
        """
        active_count = len([tx for tx in self.active_transactions.values() if tx.is_active])
        total_operations = sum(len(tx.operations) for tx in self.active_transactions.values())

        return {
            "active_transactions": active_count,
            "total_transactions": len(self.active_transactions),
            "total_operations": total_operations,
            "current_transaction_id": self.current_transaction.id if self.current_transaction else None,
            "successful_transactions": self.successful_transactions,
            "failed_transactions": self.failed_transactions
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about active transactions (alias for get_transaction_stats).

        Returns:
            Dictionary with transaction statistics
        """
        return self.get_transaction_stats()
