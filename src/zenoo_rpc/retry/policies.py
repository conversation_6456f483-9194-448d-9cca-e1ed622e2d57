"""
Retry policies for OdooFlow.

This module provides retry policies that determine when and how to retry operations.
"""

import time
from typing import Type, Union, Tuple, Callable, Optional, Set
from dataclasses import dataclass, field

from .strategies import RetryStrategy, ExponentialBackoffStrategy
from .exceptions import RetryTimeoutError


@dataclass
class RetryPolicy:
    """Retry policy configuration."""
    
    strategy: RetryStrategy = field(default_factory=lambda: ExponentialBackoffStrategy())
    retryable_exceptions: Set[Type[Exception]] = field(default_factory=set)
    non_retryable_exceptions: Set[Type[Exception]] = field(default_factory=set)
    timeout: Optional[float] = None
    retry_condition: Optional[Callable[[Exception], bool]] = None
    
    def should_retry(self, attempt: int, exception: Exception, start_time: float) -> bool:
        """Check if operation should be retried.
        
        Args:
            attempt: Current attempt number (1-based)
            exception: Exception that occurred
            start_time: Start time of first attempt
            
        Returns:
            True if should retry
        """
        # Check timeout
        if self.timeout and (time.time() - start_time) >= self.timeout:
            return False
        
        # Check strategy limits
        if not self.strategy.should_retry(attempt, exception):
            return False
        
        # Check non-retryable exceptions
        if self.non_retryable_exceptions:
            for exc_type in self.non_retryable_exceptions:
                if isinstance(exception, exc_type):
                    return False
        
        # Check retryable exceptions
        if self.retryable_exceptions:
            retryable = False
            for exc_type in self.retryable_exceptions:
                if isinstance(exception, exc_type):
                    retryable = True
                    break
            if not retryable:
                return False
        
        # Check custom retry condition
        if self.retry_condition:
            return self.retry_condition(exception)
        
        return True
    
    def get_delay(self, attempt: int) -> float:
        """Get delay for given attempt.
        
        Args:
            attempt: Attempt number (1-based)
            
        Returns:
            Delay in seconds
        """
        return self.strategy.get_delay(attempt)


class DefaultRetryPolicy(RetryPolicy):
    """Default retry policy for common scenarios."""
    
    def __init__(self):
        # Common retryable exceptions
        retryable_exceptions = {
            ConnectionError,
            TimeoutError,
            OSError,
        }
        
        # Common non-retryable exceptions
        non_retryable_exceptions = {
            ValueError,
            TypeError,
            AttributeError,
            KeyError,
            IndexError,
        }
        
        super().__init__(
            strategy=ExponentialBackoffStrategy(
                max_attempts=3,
                base_delay=1.0,
                multiplier=2.0,
                max_delay=30.0,
                jitter=True
            ),
            retryable_exceptions=retryable_exceptions,
            non_retryable_exceptions=non_retryable_exceptions,
            timeout=60.0
        )


class NetworkRetryPolicy(RetryPolicy):
    """Retry policy optimized for network operations."""
    
    def __init__(self):
        import httpx
        
        # Network-specific retryable exceptions
        retryable_exceptions = {
            ConnectionError,
            TimeoutError,
            OSError,
            httpx.ConnectError,
            httpx.TimeoutException,
            httpx.NetworkError,
            httpx.PoolTimeout,
        }
        
        # Non-retryable HTTP errors
        non_retryable_exceptions = {
            httpx.HTTPStatusError,  # Will be handled by custom condition
        }
        
        super().__init__(
            strategy=ExponentialBackoffStrategy(
                max_attempts=5,
                base_delay=0.5,
                multiplier=1.5,
                max_delay=10.0,
                jitter=True
            ),
            retryable_exceptions=retryable_exceptions,
            non_retryable_exceptions=set(),  # Use custom condition instead
            timeout=30.0,
            retry_condition=self._network_retry_condition
        )
    
    def _network_retry_condition(self, exception: Exception) -> bool:
        """Custom retry condition for network errors."""
        import httpx
        
        # Retry on specific HTTP status codes
        if isinstance(exception, httpx.HTTPStatusError):
            # Retry on server errors and rate limiting
            retryable_status_codes = {429, 500, 502, 503, 504}
            return exception.response.status_code in retryable_status_codes
        
        return True


class DatabaseRetryPolicy(RetryPolicy):
    """Retry policy optimized for database operations."""
    
    def __init__(self):
        # Database-specific retryable exceptions
        retryable_exceptions = {
            ConnectionError,
            TimeoutError,
            OSError,
        }
        
        # Add database-specific exceptions if available
        try:
            import psycopg2
            retryable_exceptions.update({
                psycopg2.OperationalError,
                psycopg2.InterfaceError,
            })
        except ImportError:
            pass
        
        super().__init__(
            strategy=ExponentialBackoffStrategy(
                max_attempts=3,
                base_delay=2.0,
                multiplier=2.0,
                max_delay=60.0,
                jitter=True
            ),
            retryable_exceptions=retryable_exceptions,
            timeout=120.0
        )


class QuickRetryPolicy(RetryPolicy):
    """Quick retry policy for fast operations."""
    
    def __init__(self):
        super().__init__(
            strategy=ExponentialBackoffStrategy(
                max_attempts=2,
                base_delay=0.1,
                multiplier=2.0,
                max_delay=1.0,
                jitter=False
            ),
            timeout=5.0
        )


class AggressiveRetryPolicy(RetryPolicy):
    """Aggressive retry policy for critical operations."""
    
    def __init__(self):
        retryable_exceptions = {
            ConnectionError,
            TimeoutError,
            OSError,
        }
        
        super().__init__(
            strategy=ExponentialBackoffStrategy(
                max_attempts=10,
                base_delay=0.5,
                multiplier=1.2,
                max_delay=30.0,
                jitter=True
            ),
            retryable_exceptions=retryable_exceptions,
            timeout=300.0  # 5 minutes
        )
