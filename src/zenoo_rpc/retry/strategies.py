"""
Retry strategies for OdooFlow.

This module provides different retry strategies with various backoff algorithms.
"""

import random
import time
from abc import ABC, abstractmethod
from typing import Optional, Union
from dataclasses import dataclass


@dataclass
class RetryAttempt:
    """Information about a retry attempt."""
    attempt_number: int
    delay: float
    exception: Optional[Exception] = None
    start_time: float = 0.0
    end_time: float = 0.0
    
    @property
    def duration(self) -> float:
        """Get attempt duration."""
        if self.end_time > 0:
            return self.end_time - self.start_time
        return 0.0


class RetryStrategy(ABC):
    """Base class for retry strategies."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        max_delay: float = 60.0,
        jitter: bool = True
    ):
        """Initialize retry strategy.
        
        Args:
            max_attempts: Maximum number of retry attempts
            max_delay: Maximum delay between attempts
            jitter: Whether to add random jitter to delays
        """
        self.max_attempts = max_attempts
        self.max_delay = max_delay
        self.jitter = jitter
    
    @abstractmethod
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number.
        
        Args:
            attempt: Attempt number (1-based)
            
        Returns:
            Delay in seconds
        """
        pass
    
    def get_delay(self, attempt: int) -> float:
        """Get delay with optional jitter.
        
        Args:
            attempt: Attempt number (1-based)
            
        Returns:
            Delay in seconds
        """
        delay = self.calculate_delay(attempt)
        
        # Apply maximum delay limit
        delay = min(delay, self.max_delay)
        
        # Add jitter if enabled
        if self.jitter and delay > 0:
            # Add ±25% jitter
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(0, delay)  # Ensure non-negative
        
        return delay
    
    def should_retry(self, attempt: int, exception: Exception) -> bool:
        """Check if should retry based on attempt and exception.
        
        Args:
            attempt: Current attempt number (1-based)
            exception: Exception that occurred
            
        Returns:
            True if should retry
        """
        return attempt < self.max_attempts


class ExponentialBackoffStrategy(RetryStrategy):
    """Exponential backoff retry strategy."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        multiplier: float = 2.0,
        max_delay: float = 60.0,
        jitter: bool = True
    ):
        """Initialize exponential backoff strategy.
        
        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay in seconds
            multiplier: Exponential multiplier
            max_delay: Maximum delay between attempts
            jitter: Whether to add random jitter to delays
        """
        super().__init__(max_attempts, max_delay, jitter)
        self.base_delay = base_delay
        self.multiplier = multiplier
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate exponential backoff delay."""
        return self.base_delay * (self.multiplier ** (attempt - 1))


class LinearBackoffStrategy(RetryStrategy):
    """Linear backoff retry strategy."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        increment: float = 1.0,
        max_delay: float = 60.0,
        jitter: bool = True
    ):
        """Initialize linear backoff strategy.
        
        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay in seconds
            increment: Linear increment per attempt
            max_delay: Maximum delay between attempts
            jitter: Whether to add random jitter to delays
        """
        super().__init__(max_attempts, max_delay, jitter)
        self.base_delay = base_delay
        self.increment = increment
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate linear backoff delay."""
        return self.base_delay + (self.increment * (attempt - 1))


class FixedDelayStrategy(RetryStrategy):
    """Fixed delay retry strategy."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        delay: float = 1.0,
        jitter: bool = True
    ):
        """Initialize fixed delay strategy.
        
        Args:
            max_attempts: Maximum number of retry attempts
            delay: Fixed delay in seconds
            jitter: Whether to add random jitter to delays
        """
        super().__init__(max_attempts, delay, jitter)
        self.delay = delay
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate fixed delay."""
        return self.delay


class AdaptiveStrategy(RetryStrategy):
    """Adaptive retry strategy that adjusts based on success rate."""
    
    def __init__(
        self,
        max_attempts: int = 5,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        jitter: bool = True,
        success_threshold: float = 0.8
    ):
        """Initialize adaptive strategy.
        
        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay in seconds
            max_delay: Maximum delay between attempts
            jitter: Whether to add random jitter to delays
            success_threshold: Success rate threshold for adaptation
        """
        super().__init__(max_attempts, max_delay, jitter)
        self.base_delay = base_delay
        self.success_threshold = success_threshold
        
        # Track success rate
        self.total_attempts = 0
        self.successful_attempts = 0
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate adaptive delay based on success rate."""
        success_rate = self.get_success_rate()
        
        if success_rate < self.success_threshold:
            # Lower success rate, increase delay
            multiplier = 2.0 ** (attempt - 1)
        else:
            # Higher success rate, use linear backoff
            multiplier = attempt
        
        return self.base_delay * multiplier
    
    def get_success_rate(self) -> float:
        """Get current success rate."""
        if self.total_attempts == 0:
            return 1.0
        return self.successful_attempts / self.total_attempts
    
    def record_attempt(self, success: bool) -> None:
        """Record attempt result for adaptation."""
        self.total_attempts += 1
        if success:
            self.successful_attempts += 1
