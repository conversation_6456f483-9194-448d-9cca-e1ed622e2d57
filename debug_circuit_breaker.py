#!/usr/bin/env python3
"""Debug script for circuit breaker."""

import asyncio
from src.zenoo_rpc.transport.pool import EnhancedConnectionPool, CircuitBreakerState
from src.zenoo_rpc.exceptions import ZenooError

async def test_circuit_breaker_blocks():
    """Test circuit breaker blocks requests when open."""
    try:
        pool = EnhancedConnectionPool(
            base_url="http://test.example.com",
            pool_size=2,
            max_connections=5
        )
        
        print(f"Initial circuit breaker state: {pool.circuit_breaker.state}")
        print(f"Should allow request: {pool.circuit_breaker.should_allow_request()}")
        
        # Manually open circuit breaker
        pool.circuit_breaker.state = CircuitBreakerState.OPEN
        
        print(f"After setting to OPEN: {pool.circuit_breaker.state}")
        print(f"Should allow request: {pool.circuit_breaker.should_allow_request()}")
        
        # Should raise error when circuit breaker is open
        try:
            connection_context = pool.get_connection()
            print(f"ERROR: get_connection() should have raised exception but returned: {connection_context}")
        except ZenooError as e:
            print(f"SUCCESS: get_connection() raised ZenooError: {e}")
        except Exception as e:
            print(f"ERROR: get_connection() raised unexpected exception: {e}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_circuit_breaker_blocks())
