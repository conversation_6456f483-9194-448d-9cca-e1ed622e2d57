#!/usr/bin/env python3
"""Debug script for cache integration."""

import asyncio
from unittest.mock import AsyncMock

from src.zenoo_rpc.query.builder import QuerySet
from src.zenoo_rpc.models.common import ResPartner

async def test_cache_methods():
    """Test cache methods exist."""
    try:
        mock_client = AsyncMock()
        
        queryset = QuerySet(ResPartner, mock_client)
        
        print(f"QuerySet type: {type(queryset)}")
        print(f"QuerySet methods: {[m for m in dir(queryset) if not m.startswith('_')]}")
        print(f"Has _generate_cache_key: {hasattr(queryset, '_generate_cache_key')}")
        print(f"Has cache: {hasattr(queryset, 'cache')}")
        
        if hasattr(queryset, '_generate_cache_key'):
            cache_key = queryset._generate_cache_key()
            print(f"Cache key: {cache_key}")
        
        if hasattr(queryset, 'cache'):
            cached_qs = queryset.cache(ttl=600)
            print(f"Cached queryset: {cached_qs}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_cache_methods())
