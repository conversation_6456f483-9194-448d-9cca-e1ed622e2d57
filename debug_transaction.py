#!/usr/bin/env python3
"""Debug script for transaction rollback."""

import asyncio
from unittest.mock import AsyncMock

from src.zenoo_rpc.transaction.manager import (
    Transaction, TransactionManager, TransactionState
)

async def test_nested_transaction_rollback():
    """Test rollback with nested transactions."""
    try:
        mock_client = AsyncMock()
        manager = TransactionManager(mock_client)
        
        print("Starting nested transaction test...")
        
        async with manager.transaction() as parent_tx:
            print(f"Parent transaction created: {parent_tx.id}")
            parent_tx.add_operation("create", "res.partner", [1], created_ids=[1])
            
            async with manager.transaction() as child_tx:
                print(f"Child transaction created: {child_tx.id}")
                child_tx.add_operation("update", "res.partner", [2], original_data={"name": "Old"})
                
                # Rollback child transaction
                print("Rolling back child transaction...")
                await child_tx.rollback()
                
                print(f"Child state: {child_tx.state}")
                print(f"Parent state: {parent_tx.state}")
                
                assert child_tx.state == TransactionState.ROLLED_BACK
                assert parent_tx.state == TransactionState.ACTIVE
        
        # Parent should still commit successfully
        print(f"Final parent state: {parent_tx.state}")
        assert parent_tx.state == TransactionState.COMMITTED
        print("Test passed!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_nested_transaction_rollback())
