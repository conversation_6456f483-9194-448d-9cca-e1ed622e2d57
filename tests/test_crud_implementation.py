"""Test CRUD implementation in ZenooClient."""

import pytest
from unittest.mock import AsyncMock, patch
from src.zenoo_rpc.client import ZenooClient
from src.zenoo_rpc.exceptions import AuthenticationError


@pytest.fixture
async def authenticated_client():
    """Create an authenticated client for testing."""
    client = ZenooClient("http://localhost:8069")

    # Mock the authentication by setting internal attributes
    client._session._database = "test_db"
    client._session._uid = 1
    client._session._username = "admin"
    client._session._password = "admin"

    # Mock execute_kw to avoid actual RPC calls
    client.execute_kw = AsyncMock()

    return client


@pytest.fixture
async def unauthenticated_client():
    """Create an unauthenticated client for testing."""
    client = ZenooClient("http://localhost:8069")
    client.execute_kw = AsyncMock()
    return client


class TestCRUDMethods:
    """Test CRUD methods implementation."""

    async def test_create_method_success(self, authenticated_client):
        """Test successful create operation."""
        # Setup
        authenticated_client.execute_kw.return_value = 123
        
        # Execute
        result = await authenticated_client.create(
            "res.partner",
            {"name": "Test Partner", "email": "<EMAIL>"}
        )
        
        # Verify
        assert result == 123
        authenticated_client.execute_kw.assert_called_once_with(
            "res.partner",
            "create",
            [{"name": "Test Partner", "email": "<EMAIL>"}],
            context=None
        )

    async def test_create_method_with_context(self, authenticated_client):
        """Test create operation with context."""
        # Setup
        authenticated_client.execute_kw.return_value = 124
        context = {"lang": "en_US", "tz": "UTC"}
        
        # Execute
        result = await authenticated_client.create(
            "res.partner",
            {"name": "Test Partner 2"},
            context=context
        )
        
        # Verify
        assert result == 124
        authenticated_client.execute_kw.assert_called_once_with(
            "res.partner",
            "create",
            [{"name": "Test Partner 2"}],
            context=context
        )

    async def test_create_method_unauthenticated(self, unauthenticated_client):
        """Test create operation when not authenticated."""
        with pytest.raises(AuthenticationError, match="Not authenticated"):
            await unauthenticated_client.create(
                "res.partner",
                {"name": "Test Partner"}
            )

    async def test_write_method_success(self, authenticated_client):
        """Test successful write operation."""
        # Setup
        authenticated_client.execute_kw.return_value = True
        
        # Execute
        result = await authenticated_client.write(
            "res.partner",
            [1, 2, 3],
            {"active": False}
        )
        
        # Verify
        assert result is True
        authenticated_client.execute_kw.assert_called_once_with(
            "res.partner",
            "write",
            [[1, 2, 3], {"active": False}],
            context=None
        )

    async def test_write_method_with_context(self, authenticated_client):
        """Test write operation with context."""
        # Setup
        authenticated_client.execute_kw.return_value = True
        context = {"check_company": True}
        
        # Execute
        result = await authenticated_client.write(
            "res.partner",
            [5],
            {"name": "Updated Name"},
            context=context
        )
        
        # Verify
        assert result is True
        authenticated_client.execute_kw.assert_called_once_with(
            "res.partner",
            "write",
            [[5], {"name": "Updated Name"}],
            context=context
        )

    async def test_write_method_unauthenticated(self, unauthenticated_client):
        """Test write operation when not authenticated."""
        with pytest.raises(AuthenticationError, match="Not authenticated"):
            await unauthenticated_client.write(
                "res.partner",
                [1],
                {"name": "Test"}
            )

    async def test_unlink_method_success(self, authenticated_client):
        """Test successful unlink operation."""
        # Setup
        authenticated_client.execute_kw.return_value = True
        
        # Execute
        result = await authenticated_client.unlink("res.partner", [1, 2, 3])
        
        # Verify
        assert result is True
        authenticated_client.execute_kw.assert_called_once_with(
            "res.partner",
            "unlink",
            [[1, 2, 3]],
            context=None
        )

    async def test_unlink_method_with_context(self, authenticated_client):
        """Test unlink operation with context."""
        # Setup
        authenticated_client.execute_kw.return_value = True
        context = {"force_delete": True}
        
        # Execute
        result = await authenticated_client.unlink(
            "res.partner",
            [10],
            context=context
        )
        
        # Verify
        assert result is True
        authenticated_client.execute_kw.assert_called_once_with(
            "res.partner",
            "unlink",
            [[10]],
            context=context
        )

    async def test_unlink_method_unauthenticated(self, unauthenticated_client):
        """Test unlink operation when not authenticated."""
        with pytest.raises(AuthenticationError, match="Not authenticated"):
            await unauthenticated_client.unlink("res.partner", [1])


class TestManagerSetup:
    """Test manager setup methods."""

    async def test_setup_transaction_manager(self, authenticated_client):
        """Test transaction manager setup."""
        with patch('src.zenoo_rpc.transaction.manager.TransactionManager') as MockTransactionManager:
            mock_manager = AsyncMock()
            MockTransactionManager.return_value = mock_manager
            
            # Execute
            result = await authenticated_client.setup_transaction_manager()
            
            # Verify
            assert result == mock_manager
            assert authenticated_client.transaction_manager == mock_manager
            MockTransactionManager.assert_called_once_with(authenticated_client)

    async def test_setup_cache_manager_memory(self, authenticated_client):
        """Test cache manager setup with memory backend."""
        with patch('src.zenoo_rpc.cache.manager.CacheManager') as MockCacheManager:
            mock_manager = AsyncMock()
            MockCacheManager.return_value = mock_manager
            
            # Execute
            result = await authenticated_client.setup_cache_manager(backend="memory")
            
            # Verify
            assert result == mock_manager
            assert authenticated_client.cache_manager == mock_manager
            mock_manager.setup_memory_cache.assert_called_once()

    async def test_setup_cache_manager_redis(self, authenticated_client):
        """Test cache manager setup with Redis backend."""
        with patch('src.zenoo_rpc.cache.manager.CacheManager') as MockCacheManager:
            mock_manager = AsyncMock()
            MockCacheManager.return_value = mock_manager
            
            # Execute
            result = await authenticated_client.setup_cache_manager(
                backend="redis",
                url="redis://localhost:6379"
            )
            
            # Verify
            assert result == mock_manager
            assert authenticated_client.cache_manager == mock_manager
            mock_manager.setup_redis_cache.assert_called_once_with(
                url="redis://localhost:6379"
            )
